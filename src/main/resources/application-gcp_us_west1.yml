spring:
  datasource:
    url: ***************************************************************************************************************************************************
    username: apkwebsite_rw
    password: 'm\O=jik~@oZ#Y]R='
    hikari:
      # 连接池最大连接数，默认是 10
      maximum-pool-size: 20
      # 链接超时时间，默认 30000(30 秒)
      connection-timeout: 3000000
      # 空闲连接存活最大时间，默认 600000(10 分钟)
      idle-timeout: 10000000
      # 连接将被测试活动的最大时间量
      validation-timeout: 5000
      # 此属性控制池中连接的最长生命周期，值 0 表示无限生命周期，默认 1800000(30 分钟)
      max-lifetime: 360000000
      # 连接到数据库时等待的最长时间(秒)
      login-timeout: 30
      # 池中维护的最小空闲连接数
      minimum-idle: 20
  ldap:
    urls: ldap://*************:3268
    base: dc=batmobi,dc=org
    username: l<PERSON><PERSON><PERSON>@batmobi.org
    password: Batmobi_2018
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

oss:
  qiniu:
    access-key: dUhJ_n9C4hwsOkRrkQJkQbSvWV99BzsE9SF421pK
    secret-key: EwlbsjtY330uaMJsorqHtYBE5MbqLZsXuCP96fL-
    bucket: djs-apk-jp

ai:
  model: deepseek-v3
  models:
    moonshot-v1-8k:
      api-key: sk-FOoXo1jVPo7yP4wu2iPbVnEK4qPSHpVt8WdxsFqpK9wixGVC
      base-url: https://api.moonshot.cn/v1/chat/completions
    deepseek-r1:
      api-key: sk-c8v2b1CUFGaLejD57vM2KgSpvABBB1A2WxFk03Ri0JMQfWJL
      base-url: https://api.lkeap.cloud.tencent.com/v1/chat/completions
    deepseek-v3:
      api-key: sk-c8v2b1CUFGaLejD57vM2KgSpvABBB1A2WxFk03Ri0JMQfWJL
      base-url: https://api.lkeap.cloud.tencent.com/v1/chat/completions
