<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">

    <contextName>apk-website</contextName>
    <property name="logback.logdir" value="/Users/<USER>/code/java/apkdatalog"/>
    <property name="access.logdir" value="/Users/<USER>/code/java/apkdatalog"/>

    <appender name="accessLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${access.logdir}/access_log.log</file>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{HH:mm:ss} %-5level [%thread][%file:%line] : %msg%n</pattern>
        </encoder>
        <!--滚动策略，按照时间滚动 TimeBasedRollingPolicy-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--文件路径,定义了日志的切分方式——把每一天的日志归档到一个文件中,以防止日志填满整个磁盘空间-->
            <FileNamePattern>${access.logdir}/access_log.%d{yyyy-MM-dd-HH}</FileNamePattern>
            <!--只保留最近30天的日志-->
            <maxHistory>30</maxHistory>
            <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
            <!--<totalSizeCap>1GB</totalSizeCap>-->
        </rollingPolicy>
    </appender>
    <appender name="async" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="accessLog" />
    </appender>
    <logger name="reactor.netty.http.server.AccessLog" level="INFO" additivity="false">
        <appender-ref ref="async"/>
    </logger>


    <!--输出到控制台 CONSOLEAppender-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!--展示格式 layout-->
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>
                <pattern>%d{HH:mm:ss.SSS} %contextName [%thread] %-5level %logger{36} - %msg%n</pattern>
            </pattern>
        </layout>
    </appender>
    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    	<!--如果只是想要 Info 级别的日志，只是过滤 info 还是会输出 Error 日志，因为 Error 的级别高，
    	所以我们使用下面的策略，可以避免输出 Error 的日志-->
      <filter class="ch.qos.logback.classic.filter.LevelFilter">
        <!--过滤 Error-->
        <level>ERROR</level>
        <!--匹配到就禁止-->
        <onMatch>DENY</onMatch>
        <!--没有匹配到就允许-->
        <onMismatch>ACCEPT</onMismatch>
      </filter>
	    <!--日志名称，如果没有File 属性，那么只会使用FileNamePattern的文件路径规则
	        如果同时有<File>和<FileNamePattern>，那么当天日志是<File>，明天会自动把今天
	        的日志改名为今天的日期。即，<File> 的日志都是当天的。
	    -->
      <File>${logback.logdir}/info.log</File>
       <!--滚动策略，按照时间滚动 TimeBasedRollingPolicy-->
      <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
        <!--文件路径,定义了日志的切分方式——把每一天的日志归档到一个文件中,以防止日志填满整个磁盘空间-->
        <FileNamePattern>${logback.logdir}/info.log_%d{yyyy-MM-dd-HH}</FileNamePattern>
        <!--只保留最近90天的日志-->
        <maxHistory>90</maxHistory>
        <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
        <!--<totalSizeCap>1GB</totalSizeCap>-->
       </rollingPolicy>
       <!--日志输出编码格式化-->
       <encoder>
           <charset>UTF-8</charset>
           <pattern>%d [%thread] %-5level %logger{36} %line - %msg%n</pattern>
       </encoder>
    </appender>


    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--如果只是想要 Error 级别的日志，那么需要过滤一下，默认是 info 级别的，ThresholdFilter-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>Error</level>
        </filter>
         <!--日志名称，如果没有File 属性，那么只会使用FileNamePattern的文件路径规则 如果同时有<File>和<FileNamePattern>，那么当天日志是<File>，明天会自动把今天 的日志改名为今天的日期。即，<File> 的日志都是当天的。
       -->
        <File>${logback.logdir}/faillog.log</File>
        <!--滚动策略，按照时间滚动 TimeBasedRollingPolicy-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
        <!--文件路径,定义了日志的切分方式——把每一天的日志归档到一个文件中,以防止日志填满整个磁盘空间-->
           <FileNamePattern>${logback.logdir}/faillog.log_%d{yyyy-MM-dd-HH}</FileNamePattern>
           <!--只保留最近90天的日志-->
          <maxHistory>90</maxHistory>
          <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
          <!--<totalSizeCap>1GB</totalSizeCap>-->
          </rollingPolicy>
          <!--日志输出编码格式化-->
        <encoder>
           <charset>UTF-8</charset>
           <pattern>%d [%thread] %-5level %logger{36} %line - %msg%n</pattern>
        </encoder>
     </appender>
     
    <!--指定最基础的日志输出级别-->
    <root level="INFO">
        <!--appender将会添加到这个loger-->
        <appender-ref ref="CONSOLE"/>
<!--        <appender-ref ref="INFO_FILE"/>-->
<!--        <appender-ref ref="ERROR_FILE"/>-->
    </root>
    
    <!-- @ApiModelProperty注解在字段上时，如果字段的类型为Long或是int类型，那么程序启动后，访问swagger-ui.html的页面，程序会报错 
      java.lang.NumberFormatException: For input string: "" -->
    <logger name="io.swagger.models.parameters.AbstractSerializableParameter" level="ERROR"/>

</configuration>