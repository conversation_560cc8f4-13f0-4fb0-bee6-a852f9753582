server:
  port: 8080
  tomcat:
    max-http-form-post-size: 100MB
    basedir: /Users/<USER>/data/logs/${spring.application.name}
    accesslog:
      enabled: true
      pattern: '%h %l %u %t "%r" %s %b %D'
      directory: ./
      file-date-format: yyyy-MM-dd-HH
      prefix: access_log.
      suffix: ''
  compression:
    enabled: true
    min-response-size: 1024
    mime-types:
      - image/png
      - image/jpeg
      - image/jpg
      - text/html
      - application/javascript
      - text/css
      - application/octet-stream
      - application/json
spring:
  application:
    name: apk-website
  profiles:
    active:
      - '@environment@'
  main:
    allow-circular-references: true
  servlet:
    multipart:
      max-request-size: 100MB
      max-file-size: 100MB

management:
  server:
    port: 18080
  metrics:
    tags:
      application: ${spring.application.name}
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      base-path: /
      exposure:
        include: "*"
      path-mapping:
        prometheus: prometheus
