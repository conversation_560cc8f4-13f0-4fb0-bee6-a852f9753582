#!/bin/sh
##
## java env
JAVA_HOME=/usr/local/java/jdk1.8.0_152

## service name
APP_NAME=apk-website

SERVICE_DIR="$(cd "$(dirname $0)";pwd)"
SERVICE_NAME=$APP_NAME
JAR_NAME=$SERVICE_NAME\.jar
PID=$SERVICE_NAME\.pid
PROFILE=$2
SKYWALKING_DIR=./skywalking
SW_CONFIG=/usr/local/skywalking/skywalking-agent/config/agent.config

if [[ -z "$JAVA_OPTIONS" ]];then
	JAVA_OPTIONS="-Xms2048m -Xmx2048m"
fi

if [[ -z "$JAR_OPTIONS" ]];then
	JAR_OPTIONS="--spring.profiles.active="$PROFILE
fi


do_start(){
   if [ -a "$SW_CONFIG" ]; then
     SKYWALKING_OPTIONS="-javaagent:/usr/local/skywalking/skywalking-agent/skywalking-agent.jar -Dskywalking.agent.service_name=$SERVICE_NAME-$PROFILE"
   fi
	[[ -z "$PROFILE" ]] && echo "[ERROR] pls. set {PROFILE}" && exit 1
    ENV=`which java 2>&1 | grep which`
	if [ ! -n "$ENV" ];then
	   nohup java $JAVA_OPTIONS -Dfile.encoding=UTF-8 $SKYWALKING_OPTIONS -jar $JAR_NAME $JAR_OPTIONS >/dev/null 2>&1 &
	else
	   nohup $JAVA_HOME/bin/java $JAVA_OPTIONS -Dfile.encoding=UTF-8 $SKYWALKING_OPTIONS -jar $JAR_NAME $JAR_OPTIONS >/dev/null 2>&1 &
	fi

    echo $! > $SERVICE_DIR/$PID
    echo "=== start $SERVICE_NAME"
}

 do_stop(){
    if [ -f $SERVICE_DIR/$PID ];then
	    P_ID=`cat $SERVICE_DIR/$PID`
	    ps -p $P_ID >/dev/null 2>&1
	    if [ "$?" = "0" ]; then
		  kill $P_ID
	    fi
	    rm -rf $SERVICE_DIR/$PID
	    echo "=== stop $SERVICE_NAME"
	fi

   sleep 15

    #再查询一遍，如果没有结束则强杀进程 * 这里有问题 改为$P_ID
	NPID=`ps aux |grep java |grep "jar $SERVICE_NAME.jar" |grep -v grep |awk '{print $2}'`
    #NPID=$P_ID
	if [ ! -n "$NPID"  ]; then
		echo "=== $SERVICE_NAME process not exists or stop success"
	else
		echo "=== $SERVICE_NAME process pid is:$NPID"
		echo "=== begin kill $SERVICE_NAME process, pid is:$NPID"
		kill -9 $NPID
	fi
}

cd ${SERVICE_DIR}

case "$1" in
    start)
		do_start
		;;
    stop)
	    do_stop
	    ;;
    restart)
	    do_stop
        sleep 2
        do_start
        echo "=== restart $SERVICE_NAME"
	    ;;
     *)
		echo "usage: $0 {start|stop|restart} {PROFILE}"
		exit 1
        ;;
    esac