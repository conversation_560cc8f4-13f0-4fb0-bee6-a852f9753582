<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apk.website.mapper.TCarInfoMapper">

    <resultMap id="BaseResultMap" type="com.apk.website.entity.TCarInfo">
            <id property="id" column="id" />
            <result property="category" column="category" />
            <result property="subCategory" column="sub_category" />
            <result property="state" column="state" />
            <result property="type" column="type" />
            <result property="city" column="city" />
            <result property="brand" column="brand" />
            <result property="title" column="title" />
            <result property="cover" column="cover" />
            <result property="location" column="location" />
            <result property="date" column="date" />
            <result property="price" column="price" />
            <result property="img" column="img" />
            <result property="detailInfo" column="detail_info" />
            <result property="description" column="description" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,category,sub_category,state,type,city,
        brand,title,cover,location,date,
        price,img,detail_info,description,create_time,
        update_time
    </sql>

    <insert id="insertBatch">
        insert into t_car_info (category,sub_category,state,type,city,brand,title,cover,location,date,price,img,detail_info,description) values
        <foreach collection="list" item="item" separator=",">
            (#{item.category},#{item.subCategory},#{item.state},#{item.type},#{item.city},#{item.brand},#{item.title},#{item.cover},#{item.location},#{item.date},#{item.price},#{item.img},#{item.detailInfo},#{item.description})
        </foreach>
    </insert>

    <select id="selectTypes" resultType="com.apk.website.entity.TCarInfo">
        SELECT id,title,cover,date,price,city,description FROM t_car_info WHERE  state = #{state} and type = #{type} LIMIT #{nums}
    </select>

    <select id="selectBrands" resultType="com.apk.website.entity.TCarInfo">
        SELECT id,title,cover,date,price,city,description FROM t_car_info WHERE state = #{state} and brand = #{brand}  LIMIT #{nums}
    </select>

    <select id="selectRandom" resultType="com.apk.website.entity.TCarInfo">
        SELECT id,title,cover,date,city,description,price,location
        FROM t_car_info t1 ,(SELECT ROUND(RAND() * (MAX(id) - MIN(id)) + MIN(id)) AS random_id
            FROM t_car_info)  t2
        WHERE t1.id >= t2.random_id
            LIMIT 16
    </select>
</mapper>
