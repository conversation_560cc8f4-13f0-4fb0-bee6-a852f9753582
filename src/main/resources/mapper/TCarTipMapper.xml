<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apk.website.mapper.TCarTipMapper">

    <resultMap id="BaseResultMap" type="com.apk.website.entity.TCarTip">
            <id property="id" column="id" />
            <result property="cover" column="cover" />
            <result property="subCategory" column="sub_category" />
            <result property="title" column="title" />
            <result property="description" column="description" />
            <result property="detail" column="detail" />
    </resultMap>

    <sql id="Base_Column_List">
        id,cover,sub_category,title,description,detail
    </sql>
    <insert id="insertBatch">
        insert into t_car_tip (cover,sub_category,title,description,detail) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.cover},#{item.subCategory},#{item.title},#{item.description},#{item.detail})
        </foreach>
    </insert>
    <update id="updateImg">
        update t_car_tip set cover = #{cover} where id = #{id}
    </update>
    <select id="listSideTips" resultType="com.apk.website.entity.TCarTip">
        SELECT t1.id, t1.cover, t1.title, t1.sub_category
        FROM t_car_tip t1
                 JOIN (
            SELECT ROUND(RAND() * (MAX(id) - MIN(id)) + MIN(id)) AS random_id
            FROM t_car_tip
        ) t2
        WHERE t1.id >= t2.random_id
            LIMIT #{nums};
    </select>
</mapper>
