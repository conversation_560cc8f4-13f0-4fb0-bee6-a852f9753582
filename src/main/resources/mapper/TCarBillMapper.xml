<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apk.website.mapper.TCarBillMapper">

    <resultMap id="BaseResultMap" type="com.apk.website.entity.TCarBill">
            <id property="id" column="id" />
            <result property="state" column="state" />
            <result property="subCategory" column="sub_category" />
            <result property="title" column="title" />
            <result property="img" column="img" />
    </resultMap>

    <sql id="Base_Column_List">
        id,state,sub_category,title,img
    </sql>
    <insert id="insertBatch">
        insert into t_car_bill (state,sub_category,title,img) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.state},#{item.subCategory},#{item.title},#{item.img})
        </foreach>
    </insert>
</mapper>
