<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apk.website.mapper.TMajorCityLocationMapper">

    <resultMap id="BaseResultMap" type="com.apk.website.entity.TMajorCityLocation">
            <id property="id" column="id" />
            <result property="majorCity" column="major_city" />
            <result property="title" column="title" />
            <result property="img" column="img" />
            <result property="person" column="person" />
            <result property="location" column="location" />
            <result property="phone" column="phone" />
            <result property="netAddress" column="net_address" />
            <result property="date" column="date" />
            <result property="map" column="map" />
            <result property="supportedMakes" column="supported_makes" />
            <result property="servicesOffered" column="services_offered" />
            <result property="amenity" column="amenity" />
            <result property="description" column="description" />
            <result property="category" column="category" />
            <result property="subCategory" column="sub_category" />
    </resultMap>

    <sql id="Base_Column_List">
        id,major_city,title,img,person,location,
        phone,net_address,date,supported_makes,services_offered,
        amenity,description,category,sub_category
    </sql>
    <insert id="insertBatch">
        INSERT INTO t_major_city_location (major_city,title,img,person,location,phone,net_address,date,map,supported_makes,services_offered,amenity,description,category,sub_category) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.majorCity},#{item.title},#{item.img},#{item.person},#{item.location},#{item.phone},#{item.netAddress},#{item.date},#{item.map},#{item.supportedMakes},#{item.servicesOffered},#{item.amenity},#{item.description},#{item.category},#{item.subCategory})
        </foreach>
    </insert>
</mapper>
