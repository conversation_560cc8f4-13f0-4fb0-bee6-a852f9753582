<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apk.website.mapper.ApkDomainRelationMapper">

    <insert id="insertBatch">
        INSERT INTO t_apk_domain_relation (apk_id, domain_id, status, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.apkId}, #{item.domainId}, #{item.status}, now(), now())
        </foreach>
    </insert>
</mapper>
