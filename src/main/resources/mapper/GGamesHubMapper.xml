<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apk.website.mapper.GGamesHubMapper">
    <select id="selectViewRank" resultType="com.apk.website.entity.GGamesHub">
        select * from g_games_hub
        <where>
            <if test="lang != null">
                lang = #{lang}
            </if>
        </where>
        order by
            CASE
                WHEN view LIKE '%M%' THEN CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(view, 'M', 1), '+', 1) AS UNSIGNED) * 1000000
                WHEN view LIKE '%k%' THEN CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(view, 'k', 1), '+', 1) AS UNSIGNED) * 1000
                WHEN view LIKE '%B%' THEN CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(view, 'B', 1), '+', 1) AS UNSIGNED) * 1000000000
                ELSE CAST(SUBSTRING_INDEX(view, '+', 1) AS UNSIGNED)
                END DESC
        <if test="count != null">
            LIMIT #{count}
        </if>
    </select>
</mapper>
