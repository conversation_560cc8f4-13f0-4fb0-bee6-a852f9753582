<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apk.website.mapper.TLocationMapper">

    <resultMap id="BaseResultMap" type="com.apk.website.entity.TLocation">
            <id property="id" column="id" />
            <result property="title" column="title" />
            <result property="img" column="img" />
            <result property="person" column="person" />
            <result property="location" column="location" />
            <result property="phone" column="phone" />
            <result property="netAddress" column="net_address" />
            <result property="date" column="date" />
            <result property="map" column="map" />
            <result property="supportedMakes" column="supported_makes" />
            <result property="servicesOffered" column="services_offered" />
            <result property="amenity" column="amenity" />
            <result property="description" column="description" />
            <result property="category" column="category" />
            <result property="subCategory" column="sub_category" />
            <result property="state" column="state" />
            <result property="city" column="city" />
            <result property="brand" column="brand" />
            <result property="commonService" column="common_service" />
    </resultMap>

    <sql id="Base_Column_List">
        id,title,img,person,location,phone,
        net_address,date,supported_makes,services_offered,amenity,
        description,category,sub_category,state,city,
        brand,common_service
    </sql>
    <insert id="insertBatch">
        INSERT INTO t_location (
        title, img, person, location, phone, net_address, date, map,
        supported_makes, services_offered, amenity, description, category,
        sub_category, state, city, brand, common_service
        ) VALUES
        <foreach item="item" collection="list" separator="," index="index">
            (
            #{item.title}, #{item.img}, #{item.person}, #{item.location}, #{item.phone},
            #{item.netAddress}, #{item.date}, #{item.map}, #{item.supportedMakes},
            #{item.servicesOffered}, #{item.amenity}, #{item.description}, #{item.category},
            #{item.subCategory}, #{item.state}, #{item.city}, #{item.brand}, #{item.commonService}
            )
        </foreach>
    </insert>
    <select id="selectNearBy" resultType="com.apk.website.entity.TLocation">
        SELECT id,title,img,person,location,phone,net_address, date,description
        FROM t_location t1 ,(SELECT ROUND(RAND() * (MAX(id) - MIN(id)) + MIN(id)) AS random_id
                             FROM t_location)  t2
        WHERE t1.id >= t2.random_id
            LIMIT #{nums}
    </select>

</mapper>
