<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apk.website.mapper.TApkRelationMapper">

    <resultMap id="BaseResultMap" type="com.apk.website.entity.TApkRelation">
            <id property="id" column="id" />
            <result property="apkId" column="apk_id" />
            <result property="domainId" column="domain_id" />
            <result property="relationApks" column="relation_apks" />
            <result property="type" column="type" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,apk_id,domain_id,relation_apks,type,create_time,
        update_time
    </sql>
    <insert id="batchInsert">
        insert into t_apk_relation (apk_id,domain_id,relation_apks,type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.apkId},#{item.domainId},#{item.relationApks},#{item.type})
        </foreach>
    </insert>
    <select id="selectListId" resultType="string">
        select relation_apks from t_apk_relation  ${ew.customSqlSegment}
    </select>
    <select id="selectDistinctPage" resultType="com.apk.website.entity.TApkRelation">
        select distinct apk_id from t_apk_relation ${ew.customSqlSegment}
    </select>
</mapper>
