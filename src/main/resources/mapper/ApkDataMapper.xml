<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.apk.website.mapper.ApkDataMapper">

    <insert id="batchInsert" parameterType="list">
        INSERT INTO t_apk_data
        (category, sub_category, app_name, img, banners, score, installs, detail, extra_info, google_url, apk_url,language, create_time, update_time) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.category},#{item.subCategory},#{item.appName},#{item.img},#{item.banners},#{item.score},#{item.installs},#{item.detail},
            #{item.extraInfo},#{item.googleUrl},#{item.apkUrl},#{item.language},now(),now()
            )
        </foreach>
    </insert>

    <select id="selectPageVo" resultType="com.apk.website.vo.ApkInfoVo">
        select id,app_name,category,sub_category,img,installs,score,bundle,status,picture,update_time
        from t_apk_data
                 ${ew.customSqlSegment}
        order by update_time desc
    </select>

    <select id="selectPageVo2" resultType="com.apk.website.vo.ApkInfoVo">
        select id,app_name,category,sub_category,img,installs,score,bundle,status,picture,update_time
        from t_apk_data
                 ${ew.customSqlSegment}
        order by
            CASE
                WHEN installs LIKE '%M%' THEN CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(installs, 'M', 1), '+', 1) AS UNSIGNED) * 1000000
                WHEN installs LIKE '%k%' THEN CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(installs, 'k', 1), '+', 1) AS UNSIGNED) * 1000
                WHEN installs LIKE '%B%' THEN CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(installs, 'B', 1), '+', 1) AS UNSIGNED) * 1000000000
                ELSE CAST(SUBSTRING_INDEX(installs, '+', 1) AS UNSIGNED)
                END DESC,score DESC
    </select>

    <select id="selectPageVo3" resultType="com.apk.website.vo.ApkInfoVo">
        select id,app_name,category,sub_category,img,installs,score,bundle,status,picture,update_time
        from t_apk_data
                 ${ew.customSqlSegment}
        order by score desc
    </select>

    <select id="selectPageVoBack" resultType="com.apk.website.vo.ApkInfoVo">
        select id,app_name,category,sub_category,img,installs,score,bundle,status,picture,update_time,reason
        from t_apk_data
                 ${ew.customSqlSegment}
    </select>


<!--    <select id="getRandAppCategory" resultType="com.apk.website.entity.TApkData">-->
<!--        select id, category, sub_category, app_name, img, score, installs, bundle, detail, picture-->
<!--        from t_apk_data-->
<!--        where status = 0 and language = #{language}-->
<!--        and id between 71689 and 74930-->
<!--        order by RAND()-->
<!--        limit #{counts};-->
<!--    </select>-->

<!--    <select id="getRandApp" resultType="com.apk.website.entity.TApkData">-->
<!--        select id,category,sub_category,app_name,img,score,installs,bundle,detail,picture-->
<!--        from t_apk_data-->
<!--        where status = 0 and language =#{language}-->
<!--        and id between 71689 and 74930-->
<!--        order by RAND()-->
<!--        limit ${counts};-->
<!--    </select>-->

    <select id="getRandAppCategory" resultType="com.apk.website.entity.TApkData">
        select id, category, sub_category, app_name, img, score, installs, bundle, detail, picture
        from t_apk_data
        where status = 0 and language = #{language} and category = #{category}
--           and id > 71689
        and id  in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by RAND()
            limit #{counts};
    </select>

    <select id="getRandApp" resultType="com.apk.website.entity.TApkData">
        select id,category,sub_category,app_name,img,score,installs,bundle,detail,picture
        from t_apk_data
        where status = 0 and language =#{language}
--           and id > 71689
        and id  in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by RAND()
            limit ${counts};
    </select>

    <select id="queryByCategory" resultType="com.apk.website.entity.TApkData">
        select id,category,sub_category,app_name,img,score,installs, extra_info, google_url, apple_url, bundle, picture
        from t_apk_data
        where language = #{language} and status = 1 and sub_category = #{subCategory} and id != #{id} and bundle !=#{id}
        limit 1
    </select>

    <select id="getByIdOrBundle" resultType="com.apk.website.entity.TApkData">
        select *
        from t_apk_data
        where language=#{language} and (id = #{id} or bundle = #{id})
        limit 1
    </select>

    <select id="getByNameOrBundle" resultType="com.apk.website.entity.TApkData">
        select id,category,sub_category,app_name,img,score,installs,bundle,detail,picture
        from t_apk_data
        where status = 0 and language =#{language} and (app_name like concat('%',#{appName},'%') or bundle like concat('%',#{appName},'%'))
    </select>

    <select id="selectByInstallsRank" resultType="com.apk.website.entity.TApkData">
        select id,category,sub_category,app_name,img,banners,score,installs,bundle,picture from t_apk_data
        <where>
            status = 0 and language = #{language}
            <if test="category != null and category != ''">
                and category = #{category}
            </if>
            <if test="subCategory != null and subCategory != ''">
                and sub_category = #{subCategory}
            </if>
            <if test="language != null and language != ''">
                and language = #{language}
            </if>
            <if test="ids != null and ids.size() > 0">
                and id  in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by
        case
        when installs like '%M%' then cast(substring_index(installs, 'M', 1) as unsigned) * 1000000
        when installs like '%k%' then cast(substring_index(installs, 'k', 1) as unsigned) * 1000
        when installs like '%B%' then cast(substring_index(installs, 'B', 1) as unsigned) * 1000000000
        else cast(installs as unsigned)
        end desc
        limit ${count}
    </select>

    <select id="selectAllCategories" resultType="java.lang.String">
        select distinct sub_category from t_apk_data
        where language = 'en'
    </select>

    <select id="select" resultType="com.apk.website.entity.TApkData">
        SELECT id,bundle,extra_info,google_url FROM t_apk_data
        WHERE `language` = 'jp' and  create_time &lt; '2025-06-10 00:00:00' and id > 70793
    </select>

    <update id="updateStatus">
        update t_apk_data set status =#{status} where id = #{id}
    </update>

    <select id="getHotGamesRandom" resultType="com.apk.website.entity.TApkData">
        select *
        from t_apk_data
        where status = 0 and language =#{language} and source = 'rank' and id  in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by RAND()
        limit #{count}
    </select>

    <select id="sameSubCategoryHotGames" resultType="com.apk.website.entity.TApkData">
            select *
            from t_apk_data
            where status = 0 and language =#{language} and category = #{category} and sub_category = #{subCategory} and source = 'rank'
            and id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
            order by RAND()
            limit #{count}
    </select>

</mapper>
