package com.apk.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("t_car_bill")
public class TCarBill {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String state;
    private String subCategory;
    private String title;
    private String img;
}