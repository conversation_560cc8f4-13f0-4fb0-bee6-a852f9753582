package com.apk.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 板块配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Getter
@Setter
@TableName("t_plate")
public class TPlate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 上级目录，0为1级目录
     */
    private Integer parentId;

    /**
     * 0开启 1关闭
     */
    private Integer status;

    /**
     * 类型：3banner/4+2等
     */
    private String type;

    /**
     * 板块名称
     */
    private String name;

    /**
     * 截图
     */
    private String screenshot;

    private Integer level;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
}
