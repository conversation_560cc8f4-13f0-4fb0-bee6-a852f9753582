package com.apk.website.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("t_location")
public class TLocation implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    private String title;


    private String img;

    private String person;

    private String location;

    private String phone;

    private String netAddress;

    private String date;
    private String map;


    private String supportedMakes;


    private String servicesOffered;

    private String amenity;

    private String description;
    private String category;

    private String subCategory;
    private String state;

    private String city;

    private String brand;

    private String commonService;
}