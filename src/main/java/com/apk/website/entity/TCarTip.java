package com.apk.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("t_car_tip")
public class TCarTip {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String cover;
    private String subCategory;
    private String title;
    private String description;
    private String detail;
}