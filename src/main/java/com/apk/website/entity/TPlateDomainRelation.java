package com.apk.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 模版_站点关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Getter
@Setter
@TableName("t_plate_domain_relation")
public class TPlateDomainRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * t_website_config主键
     */
    private Integer plateId;

    /**
     * 站点id
     */
    private Integer domainId;

    /**
     * 0apk 1文章
     */
    private Integer dataType;

    /**
     * 展示内容
     */
    private String data;

    /**
     * 展示名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 页面类型
     */
    private String pageType;

    /**
     * 板块类型
     */
    private String plateType;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    private String moreLink;

}
