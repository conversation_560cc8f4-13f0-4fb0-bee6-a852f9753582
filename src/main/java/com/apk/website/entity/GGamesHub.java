package com.apk.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * <p>
 * GamesHub游戏视频信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@TableName("g_games_hub")
@Data
public class GGamesHub implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 视频名称
     */
    private String name;

    /**
     * 封面图片URL
     */
    private String cover;

    /**
     * 视频路径
     */
    private String path;

    /**
     * 视频时长
     */
    private String duration;

    /**
     * 观看次数
     */
    private String view;

    /**
     * 系列ID
     */
    private Integer seriesId;

    /**
     * 系列名称
     */
    private String seriesName;

    /**
     * 视频播放地址
     */
    private String videoUrl;

    private String lang;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

}