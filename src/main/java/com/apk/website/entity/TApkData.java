package com.apk.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 应用信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@TableName("t_apk_data")
@Data
public class TApkData implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级分类：Apps/Games
     */
    private String category;

    /**
     * 二级分类
     */
    private String subCategory;

    /**
     * app_name
     */
    private String appName;

    /**
     * icon图标
     */
    private String img;

    /**
     * 评分
     */
    private float score;

    /**
     * 下载量
     */
    private String installs;

    /**
     * 应用描述
     */
    private String detail;

    /**
     * 额外信息
     */
    private String extraInfo;

    /**
     * 谷歌商店
     */
    private String googleUrl;

    /**
     * 苹果商店
     */
    private String appleUrl;

    /**
     * apk下载链接
     */
    private String apkUrl;

    private String offerUrl;

    /**
     * 应用内截图
     */
    private String banners;

    /**
     * banner
     */
    private String picture;

    /**
     * 包名
     */
    private String bundle;

    /**
     * 来源
     */
    private String source;

    /**
     * 0开启 1关闭
     */
    private Integer status;

    /**
     * 语言：en/jp
     */
    private String language;

    /**
     * 更新日期
     */
    private String updateDate;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    private String video;
    private String description;

    private Integer reason;

    private String versionApk;

}
