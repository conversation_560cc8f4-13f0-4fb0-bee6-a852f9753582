package com.apk.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/3
 */
@Getter
@Setter
@TableName("t_car_info")
public class TCarInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 一级分类
     */
    private String category;

    /**
     * 二级分类
     */
    private String subCategory;

    /**
     * 州
     */
    private String state;

    /**
     * 城市
     */
    private String city;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 车型
     */
    private String type;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面图
     */
    private String cover;

    /**
     * 位置
     */
    private String location;

    /**
     * 上架时间
     */
    private String date;

    /**
     * 价格
     */
    private Integer price;

    /**
     * 图片列表(逗号分隔)
     */
    private String img;

    /**
     * 详细信息
     */
    private String detailInfo;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
