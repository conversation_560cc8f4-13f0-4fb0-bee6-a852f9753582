package com.apk.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 文章内容表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
@Getter
@Setter
@TableName("t_apk_article")
public class TApkArticle implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面
     */
    private String img;

    /**
     * 内容
     */
    private String content;

    /**
     * 一级分类
     */
    private String category;

    /**
     * 二级分类
     */
    private String subCategory;

    /**
     * 备注
     */
    private String description;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 0开启 1关闭
     */
    private Integer status;
}
