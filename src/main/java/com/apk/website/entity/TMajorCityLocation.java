package com.apk.website.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("t_major_city_location")
public class TMajorCityLocation {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String majorCity;
    private String title;
    private String img;
    private String person;
    private String location;
    private String phone;
    private String netAddress;
    private String date;
    private String map;
    private String supportedMakes;
    private String servicesOffered;
    private String amenity;
    private String description;
    private String category;
    private String subCategory;
}