package com.apk.website.entity;

import lombok.Data;
import java.util.List;

@Data
public class GamesHubResponse {
    private GamesHubData data;
    private Boolean success;
    private String error_code;
    private String msg;

    @Data
    public static class GamesHubData {
        private Language language;
        private Integer count;
        private List<GameItem> list;
    }

    @Data
    public static class Language {
        private String name;
        private String code;
        private String path;
        private String bg_icon;
    }

    @Data
    public static class GameItem {
        private Integer id;
        private String name;
        private String cover;
        private String path;
        private String pc_img;
        private String mobile_img;
        private Integer series_id;
        private String series_name;
        private String duration;
        private String view;
    }
}