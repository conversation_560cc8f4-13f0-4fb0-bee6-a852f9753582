package com.apk.website.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.apk.website.entity.TApkData;
import com.apk.website.mapper.ApkDataMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/14
 */
@Slf4j
@AllArgsConstructor
public class DataListener implements ReadListener<ExcelApkData> {


    private static final int BATCH_COUNT = 100;
    private final ApkDataMapper apkDataMapper;
    /**
     * 缓存的数据
     */
    private List<ExcelApkData> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(ExcelApkData data, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSON.toJSONString(data));
        cachedDataList.add(data);
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("所有数据解析完成！");
    }

    private List<Long> getIds() {
        String filePath = "/Users/<USER>/Desktop/merge_fine.xlsx";
        List<String> bundles = new ArrayList<>();
        EasyExcel.read(filePath, ExcelApkData.class, new PageReadListener<ExcelApkData>(dataList -> {
            for (ExcelApkData ExcelApkData : dataList) {
                bundles.add(ExcelApkData.getBundle());
            }
        })).sheet().doRead();
        LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
        wrapper.in(TApkData::getBundle, bundles);
        wrapper.eq(TApkData::getLanguage, "en");
        List<TApkData> apkDataList = apkDataMapper.selectList(wrapper);
        List<Long> ids = apkDataList.stream().map(TApkData::getId).collect(Collectors.toList());
        return ids;
    }

}
