package com.apk.website.common.vo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
public class PageData<T> {

	/**当前页*/
	private int pageNum;

	/**每页的数量 */
	private int pageSize;

	/**总记录数*/
	private long total;

	/**总页数*/
	private int pages;

	/**页大小*/
	private int size;

	private List<T> list = new ArrayList<>();


//	public PageData(List<T> list) {
//		if (list instanceof Page) {
//			Page page = (Page) list;
//			this.pageNum = page.getPageNum();
//			this.total = page.getTotal();
//			this.pages = page.getPages();
//			this.size = page.size();
//			this.pageSize = page.getPageSize();
//			this.list = list;
//		}else{
//			this.pageNum =1;
//			this.total =list.size();
//			this.pages =1;
//			this.size =list.size();
//			this.pageSize =list.size();
//			this.list = list;
//		}
//	}
	public PageData(){}

	public PageData(int pageNum, int pageSize, long total, int pages, int size, List<T> list) {
		this.pageNum = pageNum;
		this.pageSize = pageSize;
		this.total = total;
		this.pages = pages;
		this.size = size;
		this.list = list;
	}

//	public void init(Page<?> page, Class<T> clazz){
//		this.pageNum = page.getPageNum();
//		this.total = page.getTotal();
//		this.pages = page.getPages();
//		this.size = page.size();
//		this.pageSize = page.getPageSize();
//		this.list = new ArrayList<>();
//		if(CollectionUtils.isNotEmpty(page)){
//			for (Object sourceObj: page){
//				try{
//					T destObj =  clazz.newInstance();
//					BeanUtils.copyProperties(sourceObj, destObj);
//					list.add(destObj);
//				}catch (Exception e){
//					throw new RuntimeException(e);
//				}
//			}
//		}
//	}
}
