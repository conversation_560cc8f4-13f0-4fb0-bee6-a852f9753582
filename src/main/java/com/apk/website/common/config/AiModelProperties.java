package com.apk.website.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/13
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "ai")
public class AiModelProperties {

    /**
     * key: 模型名称 (如 gpt-4)
     * value: 该模型对应的配置
     */
    private Map<String, ModelConfig> models;
    private String model;

    @Data
    public static class ModelConfig {
        private String apiKey;    // 必填
        private String baseUrl;   // 必填
    }

    // 根据模型名获取配置
    public ModelConfig getConfig() {
        return models.get(model);
    }

}
