package com.apk.website.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @create 2024-10-29
 */
@Configuration
@Component
@ConfigurationProperties(prefix = "oss.qiniu")
@Data
public class QiniuOssConfiguration {

    private String accessKey;
    private String secretKey;
    private String bucket;
}
