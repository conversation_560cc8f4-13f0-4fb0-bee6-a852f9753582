package com.apk.website.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;

import java.net.URI;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/22
 */
@Configuration
@EnableConfigurationProperties(CloudflareConfiguration.class)
@ConfigurationProperties(prefix = "oss.cloudflare")
@Data
public class CloudflareConfiguration {

    private String accountId;
    private String accessKey;
    private String secretKey;
    private String endpoint;

    public S3Client getS3Client() {

        AwsBasicCredentials credentials = AwsBasicCredentials.create(
                this.getAccessKey(),
                this.getSecretKey()
        );

        S3Configuration serviceConfiguration = S3Configuration.builder()
                .pathStyleAccessEnabled(true)
                .build();

        return S3Client.builder()
                .endpointOverride(URI.create(this.getEndpoint()))
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .region(Region.of("auto"))
                .serviceConfiguration(serviceConfiguration)
                .build();

    }


}
