package com.apk.website.common.utils;

import com.apk.website.common.vo.PageData;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

public class PageUtils {

    public static  <T> PageData<T> coverPage(IPage<T> page){
        PageData<T> pageData = new PageData<>();
        pageData.setPageNum((int) page.getCurrent());
        pageData.setPageSize((int)page.getSize());
        pageData.setPages((int) page.getPages());
        pageData.setTotal(page.getTotal());
        pageData.setList(page.getRecords());
        return pageData;
    }

    public static  <T> PageData<T> coverPage(IPage<T> page, List<T> records){
        PageData<T> pageData = new PageData<>();
        pageData.setPageNum((int) page.getCurrent());
        pageData.setPages((int) page.getPages());
        pageData.setTotal(page.getTotal());
        pageData.setList(records);
        return pageData;
    }
}
