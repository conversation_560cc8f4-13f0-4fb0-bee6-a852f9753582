package com.apk.website.common.utils;

import okhttp3.ConnectionPool;
import okhttp3.Dispatcher;
import okhttp3.OkHttpClient;
import okhttp3.Request;

import java.util.concurrent.TimeUnit;

/**
 * Ok http client utils
 * Created in 2022.11.09
 *
 * <AUTHOR>
 */
public enum OkHttpClientUtils {
    /**
     * Client ok http client utils.
     */
    CLIENT;

    private OkHttpClient clientInstance;

    private Integer connectTimeout_time = 60;
    private Integer writeTimeout_time = 60;
    private Integer readTimeout_time = 60;

    OkHttpClientUtils() {
        clientInstance = new OkHttpClient.Builder()
                .connectTimeout(connectTimeout_time, TimeUnit.SECONDS)
                .writeTimeout(writeTimeout_time, TimeUnit.SECONDS)
                .readTimeout(readTimeout_time, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }

    public Dispatcher dispatcher() {
        Dispatcher dispatcher = new Dispatcher();
        dispatcher.setMaxRequests(128);
        dispatcher.setMaxRequestsPerHost(32);
        return dispatcher;
    }

    public ConnectionPool pool() {
        return new ConnectionPool(128, 5L, TimeUnit.MINUTES);
    }

    public static Request buildRequest(String url) {
        Request request = new Request.Builder()
                .url(url)
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .addHeader("Accept", "*/*")
                .build();
        return request;
    }
    public static Request readImage(String url) {
        Request request = new Request.Builder()
                .url(url)
                .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .addHeader("Accept", "image/avif,image/webp,image/jpeg")
                .build();
        return request;
    }

    /**
     * Gets client instance.
     *
     * @return the client instance
     */
    public OkHttpClient getClientInstance() {
        return clientInstance;
    }

}
