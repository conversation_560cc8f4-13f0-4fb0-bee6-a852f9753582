package com.apk.website.common.utils;

import com.apk.website.dto.admin.SysUserInfo;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.JwsHeader;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SecureDigestAlgorithm;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.UUID;

public class JwtTokenHelperUtils {


    private final static SecureDigestAlgorithm<SecretKey, SecretKey> ALGORITHM = Jwts.SIG.HS256;

    private final static String SECRET = "g#a&me*goi@ng@<20>ttiiirkskms231dhfjeusmcdijdshfcds";

    /**
     * 秘钥实例
     */
    public static final SecretKey KEY = Keys.hmacShaKeyFor(SECRET.getBytes(StandardCharsets.UTF_8));
    /**
     * jwt签发者
     */
    private final static String JWT_ISS = "gamegoing";
    /**
     * jwt主题
     */
    private final static String SUBJECT = "an";

    public   final static String SYS_USER_INFO="sysUserInfo";

    public static String genAccessToken(SysUserInfo sysUserInfo, Date exprireDate) {
        // 令牌id
        String uuid = UUID.randomUUID().toString();


        return Jwts.builder()
                // 设置头部信息header
                .header()
                .add("typ", "JWT")
                .add("alg", "HS256")
                .and()
                // 设置自定义负载信息payload
                .claim("id", sysUserInfo.getId())
                .claim("username", sysUserInfo.getUsername())
                .claim("nickname", sysUserInfo.getNickname())
                .claim("sysUserGroupIds", sysUserInfo.getSysUserGroupIds())
                .claim("isSysAdmin", sysUserInfo.getIsSysAdmin())
                .claim("isDataAdmin", sysUserInfo.getIsDataAdmin())
                // 令牌ID
                .id(uuid)
                // 过期日期
                .expiration(exprireDate)
                // 签发时间
                .issuedAt(new Date())
                // 主题
                .subject(SUBJECT)
                // 签发者
                .issuer(JWT_ISS)
                // 签名
                .signWith(KEY, ALGORITHM)
                .compact();
    }

    /**
     * 解析token
     * @param token token
     * @return Jws<Claims>
     */
    public static Jws<Claims> parseClaim(String token) {
        return Jwts.parser()
                .verifyWith(KEY)
                .build()
                .parseSignedClaims(token);
    }

    public static JwsHeader parseHeader(String token) {
        return parseClaim(token).getHeader();
    }

    public static Claims parsePayload(String token) {
        return parseClaim(token).getPayload();
    }

    public static boolean isTokenExpired(Date expiration) {
        return expiration.before(new Date());
    }

    public static SysUserInfo parseFromClaims(Claims claims){
          return new SysUserInfo()
                  .setId(claims.get("id",Long.class))
                  .setUsername(claims.get("username",String.class))
                  .setNickname(claims.get("nickname",String.class))
                  .setIsDataAdmin(claims.get("isDataAdmin",Integer.class))
                  .setSysUserGroupIds(claims.get("sysUserGroupIds", String.class));
    }

    public static void main(String[] args) {
        System.out.println(Keys.hmacShaKeyFor(SECRET.getBytes(StandardCharsets.UTF_8)));
    }
}
