package com.apk.website.common.controller;

import com.apk.website.common.config.QiniuOssConfiguration;
import com.apk.website.vo.BaseResponse;
import com.qiniu.util.Auth;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/6
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/qiniu")
public class QiniuController {

    private final QiniuOssConfiguration qiniuOssConfiguration;

    @GetMapping("/getQiniuOssUploadToken")
    public BaseResponse getQiniuOssToken(String bucket) {
        Auth auth = Auth.create(qiniuOssConfiguration.getAccessKey(), qiniuOssConfiguration.getSecretKey());
        String token = auth.uploadToken(bucket);
        return BaseResponse.success(token);
    }
}
