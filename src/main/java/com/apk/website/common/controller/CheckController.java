package com.apk.website.common.controller;


import com.apk.website.vo.BaseResponse;
import com.batmobi.dataxsync.common.utils.IpUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


@RestController
@RequiredArgsConstructor
public class CheckController {

    private final Environment env;

    @GetMapping("/check")
    public BaseResponse<?> check() {
        Map<String, Object> data = new HashMap<>();
        data.put("profiles", env.getActiveProfiles()[0]);
        data.put("ip", IpUtils.getIp());
        data.put("application", "apk-website");
        return BaseResponse.success(data);
    }

}
