package com.apk.website.common.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@Data
@Accessors(chain = true)
public class PageRequest implements Serializable {

    private static final long serialVersionUID = -1771705466572683521L;

    /**
     *  当前页
     */
    private int pageNum = 1;

    /**
     *  每页的数量
     */
    private int pageSize = 10;


}
