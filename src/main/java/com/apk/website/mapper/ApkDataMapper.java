package com.apk.website.mapper;

import com.apk.website.dto.ApkDataDetailReq;
import com.apk.website.dto.ApkSearchReq;
import com.apk.website.dto.admin.ApkDataReq;
import com.apk.website.vo.ApkInfoVo;
import com.apk.website.entity.TApkData;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 应用信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-27
 */
public interface ApkDataMapper extends BaseMapper<TApkData> {
    IPage<ApkInfoVo> selectPageVo(IPage<ApkInfoVo> page, @Param(Constants.WRAPPER) Wrapper<TApkData> wrapper);

    IPage<ApkInfoVo> selectPageVo2(IPage<ApkInfoVo> page, @Param(Constants.WRAPPER) Wrapper<TApkData> wrapper);

    // 按评分排序
    IPage<ApkInfoVo> selectPageVo3(IPage<ApkInfoVo> page, @Param(Constants.WRAPPER) Wrapper<TApkData> wrapper);

    IPage<ApkInfoVo> selectPageVoBack(IPage<ApkInfoVo> page, @Param(Constants.WRAPPER) Wrapper<TApkData> wrapper);


    int updateStatus(ApkDataReq req);

    List<TApkData> getRandAppCategory(@Param("ids") List<Long> ids, @Param("category") String category, @Param("subCategory") String subCategory, @Param("language") String language, @Param("counts") Integer counts);

    List<TApkData> getRandApp(@Param("ids") List<Long> ids, @Param("language") String language, @Param("counts") Integer counts);

    TApkData queryByCategory(ApkDataDetailReq req);

    TApkData getByIdOrBundle(@Param("id") String id, @Param("language") String language);

    List<TApkData> getByNameOrBundle(ApkSearchReq req);

    List<TApkData> selectByInstallsRank(@Param("ids") List<Long>ids, @Param("category") String category, @Param("subCategory") String subCategory, @Param("count") int count, @Param("language") String language);

    List<String> selectAllCategories();

    int batchInsert(@Param("list") List<TApkData> list);

    List<TApkData> select();

    List<TApkData> getHotGamesRandom(@Param("ids") List<Long> ids,@Param("language") String language, @Param("count") int count);

    List<TApkData> sameSubCategoryHotGames(@Param("ids") List<Long> ids,@Param("category") String category, @Param("subCategory") String subCategory, @Param("language") String language, @Param("count") int count);
}
