package com.apk.website.mapper;

import com.apk.website.entity.TCarInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_car_info】的数据库操作Mapper
* @createDate 2025-06-05 09:37:38
* @Entity com.apk.website.domain.TCarInfo
*/
@Mapper
public interface TCarInfoMapper extends BaseMapper<TCarInfo> {

    int insertBatch(ArrayList<TCarInfo> tCarInfos);

    List<TCarInfo> selectTypes(@Param("type") String type , @Param("state") String state,@Param("nums") Integer i);

    List<TCarInfo> selectBrands(@Param("brand") String brand, @Param("state") String state, @Param("nums") Integer i);

    List<TCarInfo> selectRandom();

}




