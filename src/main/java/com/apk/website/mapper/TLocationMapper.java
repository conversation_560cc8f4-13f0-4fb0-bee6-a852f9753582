package com.apk.website.mapper;

import com.apk.website.entity.TLocation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_location(地理位置表)】的数据库操作Mapper
* @createDate 2025-06-05 09:33:25
* @Entity com.apk.website.domain.TLocation
*/
@Mapper
public interface TLocationMapper extends BaseMapper<TLocation> {

    int insertBatch(ArrayList<TLocation> tLocations);

    List<TLocation> selectNearBy(@Param("nums") Integer nums);
}




