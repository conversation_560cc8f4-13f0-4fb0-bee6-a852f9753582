package com.apk.website.mapper;


import com.apk.website.entity.TMajorCityLocation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;

/**
* <AUTHOR>
* @description 针对表【t_major_city_location】的数据库操作Mapper
* @createDate 2025-06-05 09:37:31
* @Entity com.apk.website.domain.TMajorCityLocation
*/
@Mapper
public interface TMajorCityLocationMapper extends BaseMapper<TMajorCityLocation> {

    int insertBatch(ArrayList<TMajorCityLocation> tMajorCityLocations);
}




