package com.apk.website.mapper;

import com.apk.website.entity.GGamesHub;
import com.apk.website.vo.GamesHubDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface GGamesHubMapper extends BaseMapper<GGamesHub> {
    List<GGamesHub> selectViewRank(@Param("count") Integer count, @Param("lang") String lang);

    /**
     * 根据name或seriesName模糊搜索
     * @param keyword 搜索关键词
     * @return 符合条件的游戏视频列表
     */
    @Select("SELECT * FROM g_games_hub " +
            "WHERE (#{lang} IS NULL OR lang = #{lang}) " +
            "AND (name LIKE CONCAT('%',#{keyword},'%') OR series_name LIKE CONCAT('%',#{keyword},'%'))")
    List<GGamesHub> searchByNameOrSeriesName(@Param("keyword") String keyword, @Param("lang") String lang);



    @Select("SELECT * FROM g_games_hub WHERE series_id = #{seriesId}")
    List<GamesHubDetailVo.Album> selectBySeriesId(@Param("seriesId") Integer seriesId);

}
