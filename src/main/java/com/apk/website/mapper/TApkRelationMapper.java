package com.apk.website.mapper;

import com.apk.website.entity.TApkRelation;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* @description 针对表【t_apk_relation】的数据库操作Mapper
* @createDate 2025-05-26 10:18:21
* @Entity com.apk.website.domain.TApkRelation
*/
@Mapper
public interface TApkRelationMapper extends BaseMapper<TApkRelation> {

    List<String> selectListId(@Param("ew") Wrapper<TApkRelation> ew);

    Page<TApkRelation> selectDistinctPage(IPage<TApkRelation> page, @Param("ew")Wrapper<TApkRelation> ew);

    int batchInsert(List<TApkRelation> tApkRelations);
}




