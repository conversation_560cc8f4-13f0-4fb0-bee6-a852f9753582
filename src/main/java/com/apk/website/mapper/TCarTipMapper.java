package com.apk.website.mapper;

import com.apk.website.entity.TCarTip;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_car_tip】的数据库操作Mapper
* @createDate 2025-06-05 09:37:25
* @Entity com.apk.website.domain.TCarTip
*/
@Mapper
public interface TCarTipMapper extends BaseMapper<TCarTip> {

    int insertBatch(ArrayList<TCarTip> tCarTips);

    int updateImg(@Param("id") int id, @Param("cover") String imgUrl);
    List<TCarTip> listSideTips(Integer nums);
}




