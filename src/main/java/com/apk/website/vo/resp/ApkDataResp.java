package com.apk.website.vo.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.apk.website.vo.ExtraInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025-03-11
 */
@Data
public class ApkDataResp {

    private Long id;
    private String category;
    private String subCategory;
    private String appName;
    private String img;
    private float score;
    private String installs;
    private String detail;
    private String googleUrl;
    private String appleUrl;
    private String apkUrl;
    private String banners;
    private String updateDate;
    private String picture;
    private int status;
    private String bundle;
    private String language;

    // extra_info
    private String email;
    private String latestVersion;
    private String offeredBy;
    private String policy;
    private String publishDate;
    private String requiresAndroid;
    private String website;

    private List<Integer> websites;

    public void buildExtraInfo(ExtraInfo extraInfo) {
        this.email = extraInfo.getEmail();
        this.latestVersion = extraInfo.getLatestVersion();
        this.offeredBy = extraInfo.getOfferedBy();
        this.policy = extraInfo.getPolicy();
        this.publishDate = extraInfo.getPublishDate();
        this.requiresAndroid = extraInfo.getRequiresAndroid();
        this.website = extraInfo.getWebsite();
    }

}
