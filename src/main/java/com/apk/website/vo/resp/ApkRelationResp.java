package com.apk.website.vo.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;
@Data
public class ApkRelationResp {
    private Long apkId;
    private String appName;
    private String category;
    private String subCategory;
    private String img;
    private String picture;
    private String installs;
    private float score;
    private String bundle;
    private Integer status;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
    private List<ApkDetailResp.RecommendApp> relationApks;

}
