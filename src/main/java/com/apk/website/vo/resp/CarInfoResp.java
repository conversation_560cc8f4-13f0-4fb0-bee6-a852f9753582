package com.apk.website.vo.resp;

import com.apk.website.entity.TCarInfo;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/6/3
 */
@Data
public class CarInfoResp {

    private Long id;

    /**
     * 州
     */
    private String state;

    /**
     * 城市
     */
    private String city;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 车型
     */
    private String type;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面图
     */
    private String cover;

    /**
     * 位置
     */
    private String location;

    /**
     * 上架时间
     */
    private String date;

    /**
     * 价格
     */
    private Integer price;

    /**
     * 图片列表(逗号分隔)
     */
    private String img;

    /**
     * 详细信息
     */
    private String detailInfo;

    /**
     * 描述
     */
    private String description;
    private List<TCarInfo> types;
    private List<TCarInfo> brands;

    private String subCategory;

    private List<TCarInfo> list10;
    private List<TCarInfo> list6;


}
