package com.apk.website.vo.resp;

import lombok.Data;

import java.util.List;

@Data
public class CfCaptchaResponse {

    private boolean success; // success 是否成功
    private List<String> errorCodes; // error-codes 错误代码列表
    private String challengeTs; // challenge_ts 挑战时间戳
    private String hostname; // hostname 主机名
    private String action; // action 动作
    private String cdata; // cdata 自定义数据
    private Metadata metadata; // metadata 元数据

    @Data
    public static class Metadata {
        private boolean interactive; // interactive 是否交互式
    }

}
