package com.apk.website.vo.resp;

import com.apk.website.entity.TCarTip;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

@Data
public class CarTipInfoResp {
    private Long id;

    private String cover;
    private String subCategory;
    private String title;
    private String description;
    private String detail;
    private List<TCarTip> relatedBlogs;
    private Long nextId;
}