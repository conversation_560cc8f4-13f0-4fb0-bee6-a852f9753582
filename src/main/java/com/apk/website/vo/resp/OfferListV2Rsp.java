package com.apk.website.vo.resp;

import com.apk.website.dto.OfferInfoV2Dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Accessors(chain = true)
public class OfferListV2Rsp {

    @JsonProperty("offer_list_size")
    private Integer offerListSize;
    @JsonProperty("total")
    private Integer total;
    @JsonProperty("code")
    private Integer code;
    @JsonProperty("mes")
    private String mes;
    @JsonProperty("page_count")
    private Integer pageCount;
    @JsonProperty("current_page")
    private Integer currentPage;
    @JsonProperty("offer_list")
    private List<OfferInfoV2Dto> offerList;

    public OfferListRsp convertOfferRsp(OfferListV2Rsp listV2Rsp) {
        OfferListRsp offerListRsp = new OfferListRsp();
        offerListRsp.setTotal(listV2Rsp.getTotal());
        offerListRsp.setPages(listV2Rsp.getPageCount());
        offerListRsp.setLimit(listV2Rsp.getOfferListSize());
        offerListRsp.setMes("");
        offerListRsp.setPage(listV2Rsp.getCurrentPage());
        offerListRsp.setStatus(listV2Rsp.getCode());
        if (listV2Rsp.getOfferList() != null && !listV2Rsp.getOfferList().isEmpty()) {
            offerListRsp.setOffers(listV2Rsp.getOfferList().stream().map(x -> x.convertInfo(x)).collect(Collectors.toList()));
        }
        return offerListRsp;
    }

}
