package com.apk.website.vo.resp;

import com.apk.website.dto.OfferInfoDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/22
 */
@Data
public class OfferListRsp {

    @JsonProperty("offers")
    private List<OfferInfoDto> offers;
    @JsonProperty("total")
    private Integer total;
    @JsonProperty("pages")
    private Integer pages;
    @JsonProperty("limit")
    private Integer limit;
    @JsonProperty("mes")
    private String mes;
    @JsonProperty("page")
    private Integer page;
    @JsonProperty("status")
    private Integer status;

}
