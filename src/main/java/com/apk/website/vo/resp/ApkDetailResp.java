package com.apk.website.vo.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.apk.website.entity.TApkArticle;
import com.apk.website.entity.TApkData;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/8
 */
@Data
public class ApkDetailResp {

    private Long id;
    private String category;
    private String subCategory;
    private String appName;
    private String img;
    private String banners;
    private float score;
    private String installs;
    private String detail;
    private List<DetailTag> detailList;
    private String extraInfo;
    private String googleUrl;
    private String appleUrl;
    private String apkUrl;
    private String rateLine;
    private String updateDate;
    private String keywords;
    private List<RecommendApp> recommendApps;
    private List<RandomApp> RandomApps;
    private List<DevApp> devApps;
    private List<TApkData> hotGames;
    private List<TApkData> sameSubCategoryHotGames;
    private TApkData otherApkData;
    private String offerUrl;
    private String bundle;
    private String picture;
    private String versionApk;
    private List<TApkArticle> articles;

    @Data
    public static class RecommendApp {
        private Long id;
        private String category;
        private String subCategory;
        private String appName;
        private String img;
        private float score;
        private String installs;
        private String bundle;
        private String picture;
    }

    @Data
    public static class RandomApp {
        private Long id;
        private String category;
        private String subCategory;
        private String appName;
        private String img;
        private float score;
        private String installs;
        private String bundle;
        private String picture;
        private String desc;
    }

    @Data
    public static class DevApp {
        private Long id;
        private String category;
        private String subCategory;
        private String appName;
        private String img;
        private float score;
        private String installs;
        private String bundle;
        private String picture;
        private String desc;
    }

    @Data
    public static class DetailTag {
        private String tag;
        private String text;
    }


}
