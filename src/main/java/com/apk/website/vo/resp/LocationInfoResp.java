package com.apk.website.vo.resp;

import com.apk.website.entity.TLocation;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class LocationInfoResp {

    private Long id;

    private String title;


    private String img;

    private String person;

    private String location;

    private String phone;

    private String netAddress;

    private String date;
    private String map;


    private String supportedMakes;


    private String servicesOffered;

    private String amenity;

    private String description;
    private String category;

    private String subCategory;
    private String state;

    private String city;

    private String brand;

    private String commonService;
    private List<TLocation> nearby;
}