package com.apk.website.vo.resp;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/22
 */
@Data
public class ApkArticleJsonResp {
    private int id;
    private String title;
    private List<Content> content;
    private String category;
    private String subCategory;
    private String domain;
    private String img;
    private Date createTime;
    private List<Article> moreArticles;
    private List<App> moreApps;

    @Data
    public static class Content {
        private String content;
        private String type;
        private String tag;
    }

    @Data
    public static class Article {
        private int id;
        private String title;
        private String img;
        private String category;
        private String subCategory;
        private String desc;
    }

    @Data
    public static class App {
        private long id;
        private String appName;
        private String img;
        private float score;
        private String category;
        private String subCategory;
        private String installs;
        private String bundle;
    }


}
