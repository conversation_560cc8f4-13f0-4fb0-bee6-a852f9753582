package com.apk.website.vo;

// 定义对应的Java类
public class TStickerPreviewData {
    private String type;
    private String id;
    private String staticUrl;
    private String fallbackStaticUrl;
    private String animationUrl;
    private String popupUrl;
    private String soundUrl;

    // getters 和 setters
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    public String getStaticUrl() { return staticUrl; }
    public void setStaticUrl(String staticUrl) { this.staticUrl = staticUrl; }
    public String getFallbackStaticUrl() { return fallbackStaticUrl; }
    public void setFallbackStaticUrl(String fallbackStaticUrl) { this.fallbackStaticUrl = fallbackStaticUrl; }
    public String getAnimationUrl() { return animationUrl; }
    public void setAnimationUrl(String animationUrl) { this.animationUrl = animationUrl; }
    public String getPopupUrl() { return popupUrl; }
    public void setPopupUrl(String popupUrl) { this.popupUrl = popupUrl; }
    public String getSoundUrl() { return soundUrl; }
    public void setSoundUrl(String soundUrl) { this.soundUrl = soundUrl; }
}
