package com.apk.website.vo;

import com.alibaba.fastjson.JSON;
import com.apk.website.dto.admin.ApkDataReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@Data
public class ExtraInfo {

    private String category;
    private String latestVersion;
    private String publishDate;
    private String requiresAndroid;
    private String offeredBy;
    private String website;
    private String email;
    private String policy;
    private String price;

    public static String buildExtraInfo(ApkDataReq req) {
        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setCategory(req.getSubCategory());
        extraInfo.setEmail(req.getEmail());
        extraInfo.setLatestVersion(req.getLatestVersion());
        extraInfo.setOfferedBy(req.getOfferedBy());
        extraInfo.setPolicy(req.getPolicy());
        extraInfo.setPublishDate(req.getPublishDate());
        extraInfo.setRequiresAndroid(req.getRequiresAndroid());
        extraInfo.setWebsite(req.getWebsite());
        return JSON.toJSONString(extraInfo);
    }

}
