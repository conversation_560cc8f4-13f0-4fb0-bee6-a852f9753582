package com.apk.website.vo;

import java.util.List;
import lombok.Data;

import java.util.Date;

@Data
public class GamesHubDetailVo {
    private Integer id;

    /**
     * 视频名称
     */
    private String name;

    /**
     * 封面图片URL
     */
    private String cover;

    /**
     * 视频路径
     */
    private String path;

    /**
     * 视频时长
     */
    private String duration;

    /**
     * 观看次数
     */
    private String view;

    /**
     * 系列ID
     */
    private Integer seriesId;

    /**
     * 系列名称
     */
    private String seriesName;

    /**
     * 视频播放地址
     */
    private String videoUrl;

    private Date createTime;

    private Date updateTime;
    private List<Album> albumList;
    @Data
    public static class Album {
        private Integer id;
        private String name;
        private String cover;
        private String path;
        private String pc_img;
        private String mobile_img;
        private Integer series_id;
        private String series_name;
        private String duration;
        private String view;
    }
}
