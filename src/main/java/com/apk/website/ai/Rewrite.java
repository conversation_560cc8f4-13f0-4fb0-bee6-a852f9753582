package com.apk.website.ai;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import com.apk.website.common.config.AiModelProperties;
import com.apk.website.common.dto.OpenAiChatReq;
import com.apk.website.common.dto.OpenAiChatResp;
import com.apk.website.common.dto.OpenAiErrorResp;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.entity.TApkData;
import com.apk.website.excel.ExcelApkData;
import com.apk.website.mapper.ApkDataMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/9
 */
@Slf4j
@Component
@AllArgsConstructor
public class Rewrite {

    private static final String PROMPT = "\n【角色】\n" +
            "请你扮演一个应用推荐网站的资深编辑，非常了解你所推荐的应用特性以及受众群体，也很擅长将其他人的描述按自己的语言进行改写，在不改变别人文章主旨的情况下，生成自己的原创内容，且会嵌入SEO关键词。\n" +
            "【任务】\n" +
            "请你仔细学习并理解写出该风格文案的技巧，并对上面的文案进行仿写。\n" +
            "【要求】\n" +
            "1.保证重写后的内容，未改变原有文章的观点，主旨一致\n" +
            "2.重写后的表述方式，符合欧美的语言习惯，语法和逻辑规则正确，内容可读，内容使用英文\n" +
            "3.应用名称必须在文章前150个字内出现至少一次，全文至少需提及2-5次\n" +
            "4.修改我原先的html标签：h2标题：需包含推荐理由或功能亮点；h3标题：细分功能介绍或应用特点；不要使用*进行加粗；必须出现p标签\n" +
            "5.改写后的文章需加入长尾关键词，如downland、ios、Android、free、Latest version、2025（时间词）等\n" +
            "6.改写全文单词数保持在1000左右\n" +
            "7.你只需要给我返回重写后的内容，不需要任何其他的解释或说明";


    private static Integer core_size = 28;
    private static Integer max_size = 28;

    private static final ExecutorService pool = new ThreadPoolExecutor(core_size, max_size,10, TimeUnit.MINUTES, new LinkedBlockingQueue<>());


    private final AiModelProperties aiModelProperties;
    private final ApkDataMapper mapper;

    public void updateByAi() {
        log.info("ai开始重写");

//        List<TApkData> apkDataList = getList();
        List<TApkData> apkDataList = mapper.select();
        List<Future<?>> futures = new ArrayList<>();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // 每批次处理20个任务
        int batchSize = 28;
        int count = 0;
        for (int i = 0; i < apkDataList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, apkDataList.size());
            List<TApkData> batchList = apkDataList.subList(i, end);

            // 提交当前批次的任务
            for (TApkData apkData : batchList) {
                futures.add(pool.submit(() -> {
                    try {
                        String rewriteDetail = rewrite(apkData);
                        apkData.setStatus(0);
                        apkData.setDetail(rewriteDetail);
                        apkData.setUpdateTime(new Date());
                        // 更新数据库中的apkData
                        mapper.updateById(apkData);
                        successCount.incrementAndGet();
                        log.info("apk: {} 重写完成", apkData.getId());
                    } catch (Exception e) {
                        failCount.incrementAndGet();
                        log.error("apk: {} 重写失败: {}", apkData.getId(), e.getMessage());
                        // 不更新数据库
                    }
                }));
            }

            // 等待当前批次的所有任务完成
            for (Future<?> future : futures) {
                try {
                    future.get(); // 阻塞直到任务完成
                } catch (InterruptedException | ExecutionException e) {
                    log.error("任务执行失败", e);
                }
            }

            // 清空 futures 列表，准备下一批任务
            futures.clear();
            log.info("批次处理完成: {} 次, 成功: {}, 失败: {}", ++count, successCount.get(), failCount.get());
        }

        log.info("ai重写完成, 总计成功: {}, 失败: {}", successCount.get(), failCount.get());
    }

    public static void main(String[] args) {

    }


    public String rewrite(TApkData apkData) throws Exception {
        String content = apkData.getDescription() + PROMPT;
        Request request = buildAiRequest(content);
        OkHttpClient client = OkHttpClientUtils.CLIENT.getClientInstance();

        try (Response response = client.newCall(request).execute()) {
            String respJson = response.body().string();
            if (!response.isSuccessful()) {
                OpenAiErrorResp resp = JSON.parseObject(respJson, OpenAiErrorResp.class);
                throw new RuntimeException("Deepseek调用失败: " + resp.getError().getMessage());
            }

            OpenAiChatResp resp = JSON.parseObject(respJson, OpenAiChatResp.class);

            if (resp.getChoices() == null || resp.getChoices().isEmpty()) {
                throw new RuntimeException("AI返回结果无效，无choices数据");
            }

            return resp.getChoices().get(0).getMessage().getContent().replaceAll("\n", "");
        } catch (Exception e) {
            throw new Exception("AI重写失败: " + e.getMessage(), e);
        }
    }

    private Request buildAiRequest(String content) {
        OpenAiChatReq req = new OpenAiChatReq();
        OpenAiChatReq.Message message = new OpenAiChatReq.Message();
        message.setRole("user");
        message.setContent(content);
        List<OpenAiChatReq.Message> messages = new ArrayList<>();
        messages.add(message);
        req.setMessages(messages);
        req.setModel(aiModelProperties.getModel());

        String json = JSON.toJSONString(req);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), json);

        return new Request.Builder().url(aiModelProperties.getConfig().getBaseUrl())
                .method("POST", body)
                .addHeader("Authorization", "Bearer " + aiModelProperties.getConfig().getApiKey())
                .build();
    }

    public List<TApkData> getList() {
        List<Long> ids = getIds();
        List<TApkData> apkDataList = mapper.selectList(new LambdaQueryWrapper<>(TApkData.class).eq(TApkData::getLanguage, "en"));
        return apkDataList.stream()
                .filter(apkData -> !ids.contains(apkData.getId()))
                .collect(Collectors.toList());
    }

    private List<Long> getIds() {
        String filePath = "/Users/<USER>/Desktop/merge_fine.xlsx";
        List<String> bundles = new ArrayList<>();
        EasyExcel.read(filePath, ExcelApkData.class, new PageReadListener<ExcelApkData>(dataList -> {
            for (ExcelApkData ExcelApkData : dataList) {
                bundles.add(ExcelApkData.getBundle());
            }
        })).sheet().doRead();
        LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
        wrapper.in(TApkData::getBundle, bundles);
        wrapper.eq(TApkData::getLanguage, "en");
        List<TApkData> apkDataList = mapper.selectList(wrapper);
        List<Long> ids = apkDataList.stream().map(TApkData::getId).collect(Collectors.toList());
        return ids;
    }

}
