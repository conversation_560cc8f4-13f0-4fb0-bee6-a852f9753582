package com.apk.website.ai;

import com.alibaba.fastjson.JSON;
import com.apk.website.common.config.AiModelProperties;
import com.apk.website.common.dto.OpenAiChatReq;
import com.apk.website.common.dto.OpenAiChatResp;
import com.apk.website.common.dto.OpenAiErrorResp;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.entity.TApkArticle;
import com.apk.website.entity.TApkData;
import com.apk.website.mapper.ApkArticleMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/16
 */
@Slf4j
@Component
@AllArgsConstructor
public class ArticleDataRewrite {

    private static Integer core_size = 24;
    private static Integer max_size = 24;

    private static final ExecutorService pool = new ThreadPoolExecutor(core_size, max_size,10, TimeUnit.MINUTES, new LinkedBlockingQueue<>());

    private static final String PROMPT = "\n【角色】\n" +
            "请你扮演一个应用推荐网站的资深编辑，非常了解你所推荐的应用特性以及受众群体，也很擅长将其他人的描述按自己的语言进行改写，在不改变别人文章主旨的情况下，生成自己的原创内容，且会嵌入SEO关键词。\n" +
            "【任务】\n" +
            "请你阅读这段json，你只需要修改type=app的content内容，此外，如果tag=img的不需要进行重写 \n" +
            "【要求】\n" +
            "1.保证重写后的内容，未改变原有文章的观点，主旨一致\n" +
            "3.你只需要给我返回重写后的内容，不需要任何其他的解释或说明";

    private final ApkArticleMapper mapper;
    private final AiModelProperties aiModelProperties;


    public void updateByAi() {
        log.info("ai开始重写");

        List<TApkArticle> articles = mapper.selectList(null);
        List<Future<?>> futures = new ArrayList<>();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);

        // 每批次处理20个任务
        int batchSize = 24;
        int count = 0;
        for (int i = 0; i < articles.size(); i += batchSize) {
            int end = Math.min(i + batchSize, articles.size());
            List<TApkArticle> batchList = articles.subList(i, end);

            // 提交当前批次的任务
            for (TApkArticle article : batchList) {
                futures.add(pool.submit(() -> {
                    try {
                        String rewriteDetail = rewrite(article);
                        article.setContent(rewriteDetail);
                        mapper.updateById(article);
                        log.info("apk: {} 重写完成",  article.getId());
                    } catch (Exception e) {
                        failCount.incrementAndGet();
                        log.error("apk: {} 重写失败: {}", article.getId(), e.getMessage());
                        // 不更新数据库
                    }
                }));
            }

            // 等待当前批次的所有任务完成
            for (Future<?> future : futures) {
                try {
                    future.get(); // 阻塞直到任务完成
                } catch (InterruptedException | ExecutionException e) {
                    log.error("任务执行失败", e);
                }
            }

            // 清空 futures 列表，准备下一批任务
            futures.clear();
            log.info("批次处理完成: {} 次, 成功: {}, 失败: {}", ++count, successCount.get(), failCount.get());
        }

        log.info("ai重写完成, 总计成功: {}, 失败: {}", successCount.get(), failCount.get());
    }

    public String rewrite(TApkArticle article) throws Exception {
        String content = article.getContent() + PROMPT;
        Request request = buildAiRequest(content);
        OkHttpClient client = OkHttpClientUtils.CLIENT.getClientInstance();

        try (Response response = client.newCall(request).execute()) {
            String respJson = response.body().string();
            if (!response.isSuccessful()) {
                OpenAiErrorResp resp = JSON.parseObject(respJson, OpenAiErrorResp.class);
                throw new RuntimeException("Deepseek调用失败: " + resp.getError().getMessage());
            }

            OpenAiChatResp resp = JSON.parseObject(respJson, OpenAiChatResp.class);

            if (resp.getChoices() == null || resp.getChoices().isEmpty()) {
                throw new RuntimeException("AI返回结果无效，无choices数据");
            }

            return resp.getChoices().get(0).getMessage().getContent().replaceAll("\n", "");
        } catch (Exception e) {
            throw new Exception("AI重写失败: " + e.getMessage(), e);
        }
    }

    private Request buildAiRequest(String content) {
        OpenAiChatReq req = new OpenAiChatReq();
        OpenAiChatReq.Message message = new OpenAiChatReq.Message();
        message.setRole("user");
        message.setContent(content);
        List<OpenAiChatReq.Message> messages = new ArrayList<>();
        messages.add(message);
        req.setMessages(messages);
        req.setModel(aiModelProperties.getModel());

        String json = JSON.toJSONString(req);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), json);

        return new Request.Builder().url(aiModelProperties.getConfig().getBaseUrl())
                .method("POST", body)
                .addHeader("Authorization", "Bearer " + aiModelProperties.getConfig().getApiKey())
                .build();
    }

}
