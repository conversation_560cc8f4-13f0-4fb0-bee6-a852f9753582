package com.apk.website.controller;

import com.apk.website.service.CarBillService;
import com.apk.website.vo.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/car/tool")
public class CarToolController {
    @Autowired
    private CarBillService carBillService;
    @RequestMapping("/list/bill")
    public BaseResponse listBill(@RequestParam String state) {
        return carBillService.listBill(state);
    }
}
