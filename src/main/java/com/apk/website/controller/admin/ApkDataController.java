package com.apk.website.controller.admin;

import com.apk.website.dto.admin.ApkDataReq;
import com.apk.website.dto.admin.DomainReq;
import com.apk.website.service.ApkDataService;
import com.apk.website.vo.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/apk")
public class ApkDataController {
    
    private final ApkDataService apkDataService;

    @PostMapping("/list")
    public BaseResponse list(@RequestBody ApkDataReq req) {
        return BaseResponse.success(apkDataService.list(req));
    }

    @PostMapping("/add")
    public BaseResponse add(@RequestBody ApkDataReq req) {
        return BaseResponse.success(apkDataService.add(req));
    }

    @PostMapping("/delete")
    public BaseResponse delete(@RequestBody ApkDataReq req) {
        return BaseResponse.success(apkDataService.delete(req));
    }

    @PostMapping("/edit")
    public BaseResponse edit(@RequestBody ApkDataReq req) {
        return BaseResponse.success(apkDataService.update(req));
    }

    @PostMapping("/getOne")
    public BaseResponse getOne(@RequestBody ApkDataReq req) {
        return BaseResponse.success(apkDataService.getOne(req));
    }

    @PostMapping("/edit/status")
    public BaseResponse editStatus(@RequestBody ApkDataReq req) {
        return BaseResponse.success(apkDataService.editStatus(req));
    }
    @GetMapping("/sub/categories")
    public BaseResponse getSubCategory(@RequestParam(required = false) String category) {
        return BaseResponse.success(apkDataService.getSubCategory(category));
    }

}
