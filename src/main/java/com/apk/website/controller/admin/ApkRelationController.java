package com.apk.website.controller.admin;

import com.apk.website.dto.admin.ApkRelationAddReq;
import com.apk.website.dto.admin.ApkRelationReq;
import com.apk.website.service.ApkRelationService;
import com.apk.website.vo.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/apk/relation")
public class ApkRelationController {
    private final ApkRelationService apkRelationService;
    @PostMapping("/list")
    public BaseResponse list(@RequestBody ApkRelationReq req) {
        return BaseResponse.success(apkRelationService.getApkRelations(req));
    }
    @PostMapping("/getOne")
    public BaseResponse getOne(@RequestBody ApkRelationReq req) {
        return BaseResponse.success(apkRelationService.getOneApkRelation(req));
    }
    @PostMapping("/save")
    public BaseResponse add(@RequestBody ApkRelationAddReq req) {
        return BaseResponse.success(apkRelationService.addApkRelation(req));
    }
//    @PostMapping("/delete")
//    public BaseResponse delete(@RequestBody ApkRelationReq req) {
//        return BaseResponse.success(apkRelationService.deleteApkRelation(req));
//    }

//    @PostMapping("/edit")
//    public BaseResponse edit(@RequestBody ApkRelationReq req) {
//        return BaseResponse.success(apkRelationService.updateApkRelation(req));
//    }


}
