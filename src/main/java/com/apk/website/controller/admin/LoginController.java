package com.apk.website.controller.admin;

import com.apk.website.dto.admin.SysUserInfo;
import com.apk.website.service.SysUserService;
import com.apk.website.vo.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/")
public class LoginController {

    private final SysUserService sysUserService;

    @PostMapping("/login")
    public BaseResponse login(@RequestBody SysUserInfo userInfo) {
        return BaseResponse.success(sysUserService.loginByLdap(userInfo.getUsername(), userInfo.getPassword()));
    }

}
