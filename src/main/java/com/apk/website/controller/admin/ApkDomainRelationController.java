package com.apk.website.controller.admin;

import com.apk.website.dto.admin.ApkDomainRelationReq;
import com.apk.website.service.ApkDomainRelationService;
import com.apk.website.vo.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @description
 * @create 2025/4/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/apk/relate")
public class ApkDomainRelationController {

    private final ApkDomainRelationService apkDomainRelationService;

    @PostMapping("/list")
    public BaseResponse list(@RequestBody ApkDomainRelationReq req) {
        return BaseResponse.success(apkDomainRelationService.list(req));
    }

    @PostMapping("/batchAdd")
    public BaseResponse batchAdd(@RequestBody ApkDomainRelationReq req) {
        return BaseResponse.success(apkDomainRelationService.batchAdd(req));
    }

    @PostMapping("/batchDel")
    public BaseResponse batchDel(@RequestBody ApkDomainRelationReq req) {
        return BaseResponse.success(apkDomainRelationService.batchDel(req));
    }

    @PostMapping("/unbind")
    public BaseResponse unbind(@RequestBody ApkDomainRelationReq req) {
        return BaseResponse.success(apkDomainRelationService.unbindApps(req));
    }


}
