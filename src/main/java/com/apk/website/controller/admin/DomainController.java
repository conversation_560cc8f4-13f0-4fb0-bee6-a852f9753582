package com.apk.website.controller.admin;

import com.apk.website.dto.admin.DomainReq;
import com.apk.website.service.DomainService;
import com.apk.website.vo.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/domain")
public class DomainController {

    private final DomainService domainService;

    @PostMapping("/list")
    public BaseResponse list(@RequestBody DomainReq req) {
        return BaseResponse.success(domainService.list(req));
    }

    @PostMapping("/add")
    public BaseResponse add(@RequestBody DomainReq req) {
        return BaseResponse.success(domainService.add(req));
    }

    @PostMapping("/delete")
    public BaseResponse delete(@RequestBody DomainReq req) {
        return BaseResponse.success(domainService.delete(req));
    }

    @PostMapping("/edit")
    public BaseResponse edit(@RequestBody DomainReq req) {
        return BaseResponse.success(domainService.update(req));
    }

    @PostMapping("/edit/status")
    public BaseResponse editStatus(@RequestBody DomainReq req) {
        return BaseResponse.success(domainService.updateStatus(req));
    }

    @PostMapping("/getOne")
    public BaseResponse getOne(@RequestBody DomainReq req) {
        return BaseResponse.success(domainService.getOne(req));
    }
}
