package com.apk.website.controller.admin;

import com.apk.website.dto.admin.plateReq;
import com.apk.website.service.PlateService;
import com.apk.website.vo.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/plate")
public class PlateController {

    private final PlateService plateService;

    @PostMapping("/list")
    public BaseResponse list(@RequestBody plateReq req) {
        return BaseResponse.success(plateService.list(req));
    }

    @PostMapping("/add")
    public BaseResponse add(@RequestBody plateReq req) {
        return BaseResponse.success(plateService.add(req));
    }

    @PostMapping("/delete")
    public BaseResponse delete(@RequestBody plateReq req) {
        return BaseResponse.success(plateService.delete(req));
    }

    @PostMapping("/edit")
    public BaseResponse edit(@RequestBody plateReq req) {
        return BaseResponse.success(plateService.update(req));
    }

    @PostMapping("/getOne")
    public BaseResponse getOne(@RequestBody plateReq req) {
        return BaseResponse.success(plateService.getOne(req));
    }

    @PostMapping("/getOneTree")
    public BaseResponse getOneTree(@RequestBody plateReq req) {
        return BaseResponse.success(plateService.getOneTree(req));
    }

    @PostMapping("/getFirstModule")
    public BaseResponse getTemplate(@RequestBody plateReq req) {
        return BaseResponse.success(plateService.getFirstModule(req));
    }


}
