package com.apk.website.controller.admin;

import com.apk.website.dto.admin.ApkArticleReq;
import com.apk.website.dto.admin.DomainReq;
import com.apk.website.service.ApkArticleService;
import com.apk.website.vo.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/article")
public class ApkArticleController {

    private final ApkArticleService apkArticleService;

    @PostMapping("/list")
    public BaseResponse list(@RequestBody ApkArticleReq req) {
        return BaseResponse.success(apkArticleService.list(req));
    }

    @PostMapping("/add")
    public BaseResponse add(@RequestBody ApkArticleReq req) {
        return BaseResponse.success(apkArticleService.add(req));
    }

    @PostMapping("/delete")
    public BaseResponse delete(@RequestBody ApkArticleReq req) {
        return BaseResponse.success(apkArticleService.delete(req));
    }

    @PostMapping("/edit")
    public BaseResponse edit(@RequestBody ApkArticleReq req) {
        return BaseResponse.success(apkArticleService.update(req));
    }

    @PostMapping("/getOne")
    public BaseResponse getOne(@RequestBody DomainReq req) {
        return BaseResponse.success(apkArticleService.getOne(req));
    }

}
