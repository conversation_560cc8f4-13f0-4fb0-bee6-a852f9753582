package com.apk.website.controller.admin;

import com.apk.website.dto.admin.PlateDomainRelationReq;
import com.apk.website.service.PlateDomainRelationService;
import com.apk.website.vo.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/6
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/plate/relation")
public class PlateDomainRelationController {

    private final PlateDomainRelationService plateDomainRelationService;

    @PostMapping("/list")
    public BaseResponse list(@RequestBody PlateDomainRelationReq req) {
        return BaseResponse.success(plateDomainRelationService.list(req));
    }

    @PostMapping("/add")
    public BaseResponse add(@RequestBody List<PlateDomainRelationReq> req) {
        return BaseResponse.success(plateDomainRelationService.add(req));
    }

    @PostMapping("/delete")
    public BaseResponse delete(@RequestBody PlateDomainRelationReq req) {
        return BaseResponse.success(plateDomainRelationService.delete(req));
    }

    @PostMapping("/edit")
    public BaseResponse edit(@RequestBody PlateDomainRelationReq req) {
        return BaseResponse.success(plateDomainRelationService.edit(req));
    }
    
}
