package com.apk.website.controller.admin;

import com.apk.website.dto.admin.TemplateReq;
import com.apk.website.service.TemplateService;
import com.apk.website.vo.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/8
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/template")
public class TemplateController {

    private final TemplateService templateService;

    @PostMapping("/list")
    public BaseResponse list(@RequestBody TemplateReq req) {
        return BaseResponse.success(templateService.list(req));
    }

    @PostMapping("/add")
    public BaseResponse add(@RequestBody TemplateReq req) {
        return BaseResponse.success(templateService.add(req));
    }

    @PostMapping("/delete")
    public BaseResponse delete(@RequestBody TemplateReq req) {
        return BaseResponse.success(templateService.delete(req));
    }

    @PostMapping("/edit")
    public BaseResponse edit(@RequestBody TemplateReq req) {
        return BaseResponse.success(templateService.update(req));
    }

    @PostMapping("/getOne")
    public BaseResponse getOne(@RequestBody TemplateReq req) {
        return BaseResponse.success(templateService.getOne(req));
    }
}
