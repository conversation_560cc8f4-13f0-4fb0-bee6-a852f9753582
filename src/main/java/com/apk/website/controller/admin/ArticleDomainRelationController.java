package com.apk.website.controller.admin;

import com.apk.website.dto.admin.ArticleDomainRelationReq;
import com.apk.website.service.ArticleDomainRelationService;
import com.apk.website.vo.BaseResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/28
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/article/relate")
public class ArticleDomainRelationController {

    private final ArticleDomainRelationService articleDomainRelationService;

    @PostMapping("/list")
    public BaseResponse list(@RequestBody ArticleDomainRelationReq req) {
        return BaseResponse.success(articleDomainRelationService.list(req));
    }

    @PostMapping("/batchAdd")
    public BaseResponse batchAdd(@RequestBody ArticleDomainRelationReq req) {
        return BaseResponse.success(articleDomainRelationService.batchAdd(req));
    }

    @PostMapping("/batchDel")
    public BaseResponse batchDel(@RequestBody ArticleDomainRelationReq req) {
        return BaseResponse.success(articleDomainRelationService.batchDel(req));
    }

    @PostMapping("/unbind")
    public BaseResponse unbind(@RequestBody ArticleDomainRelationReq req) {
        return BaseResponse.success(articleDomainRelationService.unbindArticles(req));
    }

}
