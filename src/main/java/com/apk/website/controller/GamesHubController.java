package com.apk.website.controller;

import com.apk.website.common.vo.PageData;
import com.apk.website.dto.gameshub.GamesHubDetailReq;
import com.apk.website.dto.gameshub.GamesHubHotReq;
import com.apk.website.dto.gameshub.GamesHubReq;
import com.apk.website.dto.gameshub.GamesHubSearchReq;
import com.apk.website.entity.GGamesHub;
import com.apk.website.service.GamesHubService;
import com.apk.website.vo.BaseResponse;
import com.apk.website.vo.GamesHubDetailVo;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/8/13
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/gameshub")
public class GamesHubController {

    private final GamesHubService service;

    @PostMapping("/page/list")
    public BaseResponse pageList(@RequestBody GamesHubReq req) {
        PageData<GGamesHub> result = service.list(req);
        return BaseResponse.success(result);
    }

    @PostMapping("/hot")
    public BaseResponse hot(@RequestBody GamesHubHotReq req) {
        List<GGamesHub> result = service.hot(req);
        return BaseResponse.success(result);
    }

    @PostMapping("/search")
    public BaseResponse searchByNameOrSeriesName(@RequestBody GamesHubSearchReq req) {
        List<GGamesHub> result = service.searchByNameOrSeriesName(req);
        return BaseResponse.success(result);
    }

    @PostMapping("/detail")
    public BaseResponse detail(@RequestBody GamesHubDetailReq req) {
        GamesHubDetailVo result = service.getOne(req);
        return BaseResponse.success(result);
    }
}
