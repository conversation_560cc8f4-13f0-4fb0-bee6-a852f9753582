package com.apk.website.controller;

import com.apk.website.dto.*;
import com.apk.website.dto.admin.DomainReq;
import com.apk.website.dto.admin.PlateDomainRelationReq;
import com.apk.website.service.ApkDataService;
import com.apk.website.service.PlateDomainRelationService;
import com.apk.website.vo.BaseResponse;
import com.apk.website.vo.PlateDomainRelationVo;
import com.apk.website.vo.resp.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/6
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/apk")
public class ApkController {

    private final ApkDataService apkDataService;
    private final PlateDomainRelationService plateDomainRelationService;

    @PostMapping("/cf/verify")
    public BaseResponse cfVerify(@RequestBody ApkVerifyReq req) {
        CfCaptchaResponse result = apkDataService.cfVerify(req);
        return BaseResponse.success(result);
    }

    @PostMapping("/siteverify")
    public BaseResponse siteVerify(@RequestBody ApkVerifyReq req) {
        ApkVerifyResp result = apkDataService.siteVerify(req);
        return BaseResponse.success(result);
    }

    @PostMapping("/homepage")
    public BaseResponse homepage(@RequestBody PlateDomainRelationReq req) {
        List<PlateDomainRelationVo> result = plateDomainRelationService.homepage(req);
        return BaseResponse.success(result);
    }

    @PostMapping("/detail")
    public BaseResponse detail(@RequestBody ApkDataDetailReq req) {
        return BaseResponse.success(apkDataService.detail(req));
    }

    @PostMapping("/dev/detail")
    public BaseResponse<?> devDetail(@RequestBody ApkDataDetailReq req) {
        return BaseResponse.success(apkDataService.detail(req));
    }

    @PostMapping("/search")
    public BaseResponse search(@RequestBody ApkSearchReq req) {
        ApkSearchResp result = apkDataService.search(req);
        return BaseResponse.success(result);
    }

    @PostMapping("/search/page")
    public BaseResponse<?> searchPage(@RequestBody ApkSearchReq req) {
        ApkSearchIndexResp result = apkDataService.searchPage(req);
        return BaseResponse.success(result);
    }

    @PostMapping("/getIconByCategory")
    public BaseResponse<?> getIconByCategory(@RequestBody ApkDataDetailReq req) {
        Map<String, String> result = apkDataService.getIconByCategory(req);
        return BaseResponse.success(result);
    }

    @PostMapping("/getByCategories")
    public BaseResponse getByCategories(@RequestBody HomePageReq req) {
        Map<String, List<ApkHomePageResp>> result = apkDataService.getByCategories(req);
        return BaseResponse.success(result);
    }

    @PostMapping("/getCategories")
    public BaseResponse<?> getCategories(@RequestBody TopAppsReq req) {
        List<String> result = apkDataService.getCategories(req);
        return BaseResponse.success(result);
    }

    @PostMapping("/page")
    public BaseResponse<?> page(@RequestBody TopAppsReq req) {
        return BaseResponse.success(apkDataService.page(req));
    }

    @PostMapping("/getTopDownload")
    public BaseResponse<?> getTopDownload(@RequestBody TopAppsReq req) {
        return BaseResponse.success(apkDataService.getTopDownload(req));
    }

    @PostMapping("/pages")
    public BaseResponse<?> pages(@RequestBody TopAppsReq req) {
        return BaseResponse.success(apkDataService.pages(req));
    }

}
