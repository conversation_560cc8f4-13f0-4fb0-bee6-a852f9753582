package com.apk.website.controller;

import com.apk.website.dto.ArticleReq;
import com.apk.website.dto.admin.ApkArticleReq;
import com.apk.website.entity.TApkDomainRelation;
import com.apk.website.entity.TDomain;
import com.apk.website.service.ApkArticleService;
import com.apk.website.vo.BaseResponse;
import com.apk.website.vo.resp.ApkArticleJsonResp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @description
 * @create 2025/5/8
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/article")
public class ArticleController {

    private final ApkArticleService articleService;

    @PostMapping("/list")
    public BaseResponse list(@RequestBody ApkArticleReq req) {
        return BaseResponse.success(articleService.articleList(req));
    }

    @PostMapping("/detail/json")
    public BaseResponse<?> articleDetailByJson(@RequestBody ArticleReq req) {
        ApkArticleJsonResp result = articleService.articleDetailByJson(req);
        return BaseResponse.success(result);
    }


}
