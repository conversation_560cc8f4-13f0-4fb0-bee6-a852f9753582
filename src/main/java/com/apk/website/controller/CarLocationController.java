package com.apk.website.controller;

import com.apk.website.dto.LocationReq;
import com.apk.website.dto.MajorCityReq;
import com.apk.website.schedule.CarsSchedule;
import com.apk.website.service.LocationService;
import com.apk.website.service.MajorCityLocationService;
import com.apk.website.vo.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/car/location")
public class CarLocationController {
    @Autowired
    private LocationService locationService;
    @Autowired
    private MajorCityLocationService majorCityLocationService;
    @PostMapping("/list")
    public BaseResponse list(@RequestBody LocationReq req) {
        return  locationService.list(req);
    }
    @RequestMapping("/getOne")
    public BaseResponse getOne(@RequestParam Long id) {
        return locationService.getOneLocation(id);
    }
    @PostMapping("/major/city/list")
    public BaseResponse majorCityList(@RequestBody MajorCityReq req) {
        return  majorCityLocationService.listMajorCity(req);
    }
    @RequestMapping("/major/city/getOne")
    public BaseResponse getOneMajorCity(@RequestParam Long id) {
        return majorCityLocationService.getOneMajorCity(id);
    }

}
