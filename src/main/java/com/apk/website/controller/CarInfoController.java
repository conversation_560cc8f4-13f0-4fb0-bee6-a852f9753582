package com.apk.website.controller;

import com.apk.website.dto.CarInfoReq;
import com.apk.website.dto.LocationReq;
import com.apk.website.service.CarInfoService;
import com.apk.website.vo.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/car/info")
public class CarInfoController {
    @Autowired
    private CarInfoService carInfoService;
    @PostMapping("/list")
    public BaseResponse listCarInfo(@RequestBody CarInfoReq req){
        return carInfoService.listCarInfo(req);
    }
    @PostMapping("/getOne")
    public BaseResponse getOne(@RequestBody CarInfoReq req) {
        return carInfoService.getOneCarInfo(req);
    }
}
