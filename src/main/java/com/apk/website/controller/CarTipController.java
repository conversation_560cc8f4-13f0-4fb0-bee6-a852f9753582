package com.apk.website.controller;

import com.apk.website.service.CarTipService;
import com.apk.website.vo.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/car/tip")
public class CarTipController {
    @Autowired
    CarTipService  carTipService;
    @RequestMapping("/list")
    public BaseResponse list(@RequestParam(required = false) String subCategory) {
        return carTipService.list(subCategory);
    }
    @RequestMapping("/sideTips")
    public BaseResponse listSideTips(@RequestParam(required = false) Integer nums) {
        return carTipService.listSideTips(nums);
    }
    @RequestMapping("/getOne")
    public BaseResponse getOne(@RequestParam Long id) {
        return carTipService.getOneTip(id);
    }
}
