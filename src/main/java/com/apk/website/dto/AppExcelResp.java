package com.apk.website.dto;

import lombok.Data;

import java.util.List;

@Data
public class AppExcelResp {

    private List<PageResult> result;

    @Data
    public static class PageResult {
        private String pageType;
        private List<Item> items;
    }

    @Data
    public static class Item {
        private String bannerType;
        private List<AppData> apps;
    }

    @Data
    public static class AppData {
        private int sort;
        private String bundle;
        private String appName;
        private String banners;
        private String googleUrl;
        private String appleUrl;
        private String img;
        private float score;
        private String installs;
        private String desc;
        private String category;
        private String subCategory;
        private String ThirdCategory;
    }

}
