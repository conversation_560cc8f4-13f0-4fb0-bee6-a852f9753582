package com.apk.website.dto.admin;

import com.apk.website.common.dto.PageRequest;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@Data
public class ApkArticleReq extends PageRequest {

    private Integer id;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面
     */
    private String img;

    /**
     * 内容
     */
    private String content;

    /**
     * 一级分类
     */
    private String category;

    /**
     * 二级分类
     */
    private String subCategory;

    /**
     * 备注
     */
    private String description;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    /**
     * 0开启 1关闭
     */
    private Integer status;

    private List<Integer> ids;

    private String domain;

}
