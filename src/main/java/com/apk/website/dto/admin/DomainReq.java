package com.apk.website.dto.admin;

import com.apk.website.common.dto.PageRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@Data
public class DomainReq extends PageRequest {

    private Integer id;

    /**
     * 站点名称
     */
    private String name;

    /**
     * 站点域名
     */
    private String domain;

    /**
     * 0开启 1锁定
     */
    private Integer status;

    private List<Integer> ids;

}
