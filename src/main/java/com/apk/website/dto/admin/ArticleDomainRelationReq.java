package com.apk.website.dto.admin;

import com.apk.website.common.dto.PageRequest;
import jdk.internal.dynalink.linker.LinkerServices;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/29
 */
@Data
public class ArticleDomainRelationReq extends PageRequest {

    private Long id;

    /**
     * t_article主键
     */
    private Integer articleId;

    /**
     * t_domain主键
     */
    private Integer domainId;

    /**
     * 0开启 1关闭
     */
    private Integer status;

    private List<Long> ids;

    private List<Integer> articleIds;
}
