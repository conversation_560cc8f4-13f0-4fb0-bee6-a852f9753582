package com.apk.website.dto.admin;

import com.apk.website.common.vo.PageData;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@Data
public class

ApkDataReq extends PageData {

    private Long id;

    /**
     * 一级分类：Apps/Games
     */
    private String category;

    /**
     * 二级分类
     */
    private String subCategory;

    /**
     * app_name
     */
    private String appName;

    /**
     * icon图标
     */
    private String img;

    /**
     * 评分
     */
    private float score;

    /**
     * 下载量
     */
    private String installs;

    /**
     * 应用描述
     */
    private String detail;

    /**
     * 额外信息
     */
    private String extraInfo;

    /**
     * 谷歌商店
     */
    private String googleUrl;

    /**
     * 苹果商店
     */
    private String appleUrl;

    /**
     * apk下载链接
     */
    private String apkUrl;

    private String offerUrl;

    /**
     * 应用内截图
     */
    private String banners;

    /**
     * banner
     */
    private String picture;

    /**
     * 包名
     */
    private String bundle;

    /**
     * 来源
     */
    private String source;

    /**
     * 0开启 1关闭
     */
    private Integer status;

    /**
     * 语言：en/jp
     */
    private String language;

    /**
     * 更新日期
     */
    private String updateDate;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private String email;

    private String latestVersion;

    private String offeredBy;

    private String policy;

    private String publishDate;

    private String requiresAndroid;

    private String website;

    private List<Integer> ids;

    private String sortField;

    private String sortType;

    private List<Integer> websites;

    private Integer domainId;

    private Integer reason;

    private Boolean hasPicture;
}
