package com.apk.website.dto.admin;

import com.apk.website.common.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/7
 */
@Data
public class PlateDomainRelationReq extends PageRequest {

    private Integer id;

    /**
     * t_website_config主键
     */
    private Integer plateId;

    /**
     * 站点id
     */
    private Integer domainId;

    /**
     * 站点
     */
    private String domain;

    /**
     * 0apk 1文章
     */
    private Integer dataType;

    /**
     * 展示内容
     */
    private String data;

    /**
     * 展示名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 页面类型
     */
    private String pageType;

    /**
     * 板块类型
     */
    private String plateType;

    private List<Integer> ids;

    private String moreLink;

    private Integer domainStatus;
}
