package com.apk.website.dto.admin;

import com.apk.website.common.dto.PageRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/28
 */
@Data
@Accessors(chain = true)
public class plateReq extends PageRequest {

    private Integer id;

    /**
     * 上级目录，0为1级目录
     */
    private Integer parentId;

    /**
     * 板块名称
     */
    private String name;

    /**
     * 板块类型
     */
    private String type;

    /**
     * 展示图
     */
    private String screenshot;

    /**
     * 0开启 1关闭
     */
    private Integer status;

    private List<Integer> ids;

    private Integer level;
}
