package com.apk.website.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@Data
public class OfferInfoV2Dto {
    @JsonProperty("imp_urls")
    private List<String> impUrls;
    @JsonProperty("os")
    private String os;
    @JsonProperty("undeliverable_countries")
    private List<String> undeliverableCountries;
    @JsonProperty("whitelist_channel")
    private List<String> whitelistChannel;
    @JsonProperty("billing_plan")
    private String billingPlan;
    @JsonProperty("deliverable_countries")
    private List<String> deliverableCountries;
    @JsonProperty("unit_price")
    private BigDecimal unitPrice;
    @JsonProperty("offer_name")
    private String offerName;
    @JsonProperty("offer_id")
    private String offerId;
    @JsonProperty("url")
    private String url;
    @JsonProperty("app_name")
    private String appName;
    @JsonProperty("preview_url")
    private String previewUrl;
    @JsonProperty("budget_status")
    private String budgetStatus;
    @JsonProperty("daily_toplimit")
    private BigDecimal dailyToplimit;
    @JsonProperty("blacklist_channel")
    private List<String> blacklistChannel;
    @JsonProperty("details")
    private String details;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("ads_bcost")
    private String adsBcost;
    @JsonProperty("app_pkg")
    private String appPkg;
    @JsonProperty("remarks")
    private String remarks;
    @JsonProperty("pb_event")
    private String pbEvent;

    public OfferInfoDto convertInfo(OfferInfoV2Dto offerInfoV2Dto) {
        OfferInfoDto offerInfoDto = new OfferInfoDto();
        offerInfoDto.setMobilePlatform(offerInfoV2Dto.getOs());
        offerInfoDto.setNotes(offerInfoV2Dto.getRemarks());
        offerInfoDto.setUncountries(offerInfoV2Dto.getUndeliverableCountries());
        offerInfoDto.setWhitelistPublisherids(Lists.newArrayList());
        offerInfoDto.setIcon("");
        offerInfoDto.setBanner(Maps.newHashMap());
        offerInfoDto.setBlacklistPublisherids(Lists.newArrayList());
        offerInfoDto.setCountries(offerInfoV2Dto.getDeliverableCountries());
        offerInfoDto.setIntent(0);
        offerInfoDto.setPayoutCurrency(offerInfoV2Dto.getCurrency());
        offerInfoDto.setMobileAppId(offerInfoV2Dto.getAppPkg());
        offerInfoDto.setAcquisitionFlow(offerInfoV2Dto.getBillingPlan());
        offerInfoDto.setDailyCap(offerInfoV2Dto.getDailyToplimit());
        offerInfoDto.setType("private".equals(offerInfoV2Dto.getBudgetStatus()) ? 1 : 2);
        offerInfoDto.setAppname("");
        offerInfoDto.setMobileMaxVersion("");
        offerInfoDto.setPreviewUrl(offerInfoV2Dto.getPreviewUrl());
        offerInfoDto.setName(offerInfoV2Dto.getOfferName());
        offerInfoDto.setCampId(offerInfoV2Dto.getOfferId());
        offerInfoDto.setOfferTag("");
        offerInfoDto.setMobileMinVersion("");
        offerInfoDto.setClickUrl(offerInfoV2Dto.getUrl());
        offerInfoDto.setPayoutAmount(offerInfoV2Dto.getUnitPrice());
        offerInfoDto.setPreCbid(offerInfoV2Dto.getAdsBcost());
        offerInfoDto.setImpressionUrls(offerInfoV2Dto.getImpUrls());
        offerInfoDto.setDesc("");
        offerInfoDto.setPbEvent(offerInfoV2Dto.getPbEvent());
        return offerInfoDto;
    }
}
