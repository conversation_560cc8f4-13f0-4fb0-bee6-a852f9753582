package com.apk.website.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Offer info
 * Created in 2022.11.09
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class OfferInfoDto {

    @JsonProperty("mobile_platform")
    private String mobilePlatform;
    @JsonProperty("notes")
    private String notes;
    @JsonProperty("uncountries")
    private List<String> uncountries;
    @JsonProperty("whitelist_publisherids")
    private List<String> whitelistPublisherids;
    @JsonProperty("icon")
    private String icon;
    @JsonProperty("banner")
    private Map<String, List<String>> banner;
    @JsonProperty("blacklist_publisherids")
    private List<String> blacklistPublisherids;
    @JsonProperty("countries")
    private List<String> countries;
    @JsonProperty("intent")
    private Integer intent;
    @JsonProperty("payout_currency")
    private String payoutCurrency;
    @JsonProperty("mobile_app_id")
    private String mobileAppId;
    @JsonProperty("acquisition_flow")
    private String acquisitionFlow;
    @JsonProperty("type")
    private Integer type;
    @JsonProperty("daily_cap")
    private BigDecimal dailyCap;
    @JsonProperty("appname")
    private String appname;
    @JsonProperty("mobile_max_version")
    private String mobileMaxVersion;
    @JsonProperty("preview_url")
    private String previewUrl;
    @JsonProperty("name")
    private String name;
    @JsonProperty("camp_id")
    private String campId;
    @JsonProperty("offer_tag")
    private String offerTag;
    @JsonProperty("mobile_min_version")
    private String mobileMinVersion;
    @JsonProperty("click_url")
    private String clickUrl;
    @JsonProperty("payout_amount")
    private BigDecimal payoutAmount;
    @JsonProperty("pre_cbid")
    private String preCbid;
    @JsonProperty("impression_urls")
    private List<String> impressionUrls;
    @JsonProperty("desc")
    private String desc;
    @JsonProperty("pb_event")
    private String pbEvent;

}
