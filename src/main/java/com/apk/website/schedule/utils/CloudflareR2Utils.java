package com.apk.website.schedule.utils;

import com.apk.website.common.config.CloudflareConfiguration;
import com.apk.website.common.utils.OkHttpClientUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import org.springframework.stereotype.Component;

import java.net.URI;

/**
 * <AUTHOR>
 * @description Cloudflare R2 Client封装
 * @create 2025-03-18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CloudflareR2Utils {

    @Autowired
    CloudflareConfiguration cloudflareConfiguration;

    /**
     * 上传文件到 Cloudflare R2
     */
    public String uploadToCloudflare(String url, String key) throws Exception {
        Response response = null;
        try {
            // 下载文件内容
            S3Client s3Client = cloudflareConfiguration.getS3Client();
            Request request = OkHttpClientUtils.buildRequest(url);
            response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute();
            byte[] bytes = response.body().bytes();

            // 上传到 Cloudflare R2
            PutObjectRequest req = PutObjectRequest.builder()
                    .bucket("djs-apkdest")
//                    .contentType(response.header("Content-Type", "application/octet-stream"))
                    .key(key)
                    .build();

            RequestBody requestBody = RequestBody.fromBytes(bytes);
            s3Client.putObject(req, requestBody);

            return key;
        } catch (Exception e) {
            log.error("上传文件到 Cloudflare 失败, url: {}，异常: {}", url, e.getMessage());
            throw new Exception("上传到 Cloudflare R2 失败", e);
        }finally {
            response.close();
        }
    }

}

