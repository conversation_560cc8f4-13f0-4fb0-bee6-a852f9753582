package com.apk.website.schedule.utils;

import com.apk.website.common.config.QiniuOssConfiguration;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.qiniu.common.QiniuException;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.UploadManager;
import com.qiniu.util.Auth;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.ResponseBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/22
 */
@Slf4j
@Component
public class QiniuUtils {

    @Autowired
    private QiniuOssConfiguration qiniuOssConfiguration;

    public static final Configuration configuration = new Configuration();
    public static final UploadManager uploadManager = new UploadManager(configuration);

    /**
     * 上传原图到七牛云
     */
    public String downloadAndUpload(String fileUrl, String key, String token, int maxRetries) throws Exception {
        Request request = OkHttpClientUtils.readImage(fileUrl);

        int attempt = 0;
        while (attempt < maxRetries) {
            try {
                okhttp3.Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute();
                if (!response.isSuccessful()) {
                    throw new IOException("文件下载失败，HTTP 状态码：" + response.code());
                }
                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    throw new IOException("文件下载失败，响应体为空");
                }

                try (InputStream inputStream = responseBody.byteStream()) {
                    MediaType contentType = Objects.requireNonNull(responseBody.contentType() == null ? MediaType.parse("application/octet-stream") : responseBody.contentType());
                    return uploadToQiniu(inputStream, key, token, contentType);
                }
            } catch (IOException e) {
                attempt++;
                token = getQiniuToken();
                System.out.println("尝试 " + attempt + " 次失败，原因：" + e.getMessage());
                if (attempt == maxRetries) {
                    throw new Exception("文件下载和上传失败，达到最大重试次数：" + maxRetries, e);
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    log.error("线程被中断，更新操作提前终止：{}", ie.getMessage());
                }
            }
        }
        throw new Exception("文件下载和上传失败，达到最大重试次数：" + maxRetries);
    }

    /**
     * 从获取原图并修改为webp格式重新上传
     */
    public String downloadAndUploadWebp(String fileUrl, String key, String token, int maxRetries) throws Exception {
        String url ="http://sufsaj0u2.sabkt.gdipper.com/" + fileUrl + "?imageMogr2/format/webp";
        Request request = OkHttpClientUtils.buildRequest(url);

        int attempt = 0;
        while (attempt < maxRetries) {
            try {
                okhttp3.Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute();
                if (!response.isSuccessful()) {
                    throw new IOException("文件下载失败，HTTP 状态码：" + response.code());
                }

                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    throw new IOException("文件下载失败，响应体为空");
                }

                try (InputStream inputStream = responseBody.byteStream()) {
                    return uploadToQiniu(inputStream, key, token, responseBody.contentType());
                }
            } catch (IOException e) {
                attempt++;
                token = getQiniuToken();
                if (attempt == maxRetries) {
                    throw new Exception("文件下载和上传失败，达到最大重试次数：" + maxRetries, e);
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    log.error("线程被中断，更新操作提前终止：{}", ie.getMessage());
                }
            }
        }
        throw new Exception("文件下载和上传失败，达到最大重试次数：" + maxRetries);
    }

    /**
     * 上传图片
     */
    private String uploadToQiniu(InputStream inputStream, String key, String uploadToken, MediaType contentType) throws QiniuException {
        uploadManager.put(inputStream, key, uploadToken, null, contentType.toString());
        return key;
    }

    /**
     * 获取七牛云token
     */
    public String getQiniuToken() {
        Auth auth = Auth.create(qiniuOssConfiguration.getAccessKey(), qiniuOssConfiguration.getSecretKey());
        return auth.uploadToken(qiniuOssConfiguration.getBucket());
    }

}
