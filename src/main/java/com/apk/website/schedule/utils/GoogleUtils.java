package com.apk.website.schedule.utils;

import com.alibaba.fastjson.JSON;
import com.apk.website.common.config.CloudflareConfiguration;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.entity.TApkData;
import com.apk.website.vo.ExtraInfo;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.s3.S3Client;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/22
 */
@Slf4j
@Component
public class GoogleUtils {

//    @Autowired
//    private S3Client s3Client;

    @Autowired
    private CloudflareR2Utils r2Utils;

    @Autowired
    private QiniuUtils qiniuUtils;

    private static final ThreadPoolExecutor excutor = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 2,
            Runtime.getRuntime().availableProcessors() * 2,
            100L, TimeUnit.MINUTES,
            new ArrayBlockingQueue<>(50),
            new ThreadPoolExecutor.AbortPolicy());

    public String buildBannersByCf(Document detailDoc) {
        Elements elements = detailDoc.select("div.ULeU3b").select("img.T75of");

        List<Future<String>> futures = new ArrayList<>();

        for (Element imgElement : elements) {
            String imgHref = imgElement.attr("src");
            String uuid = UUID.randomUUID().toString();
            Future<String> future = excutor.submit(() -> r2Utils.uploadToCloudflare(imgHref, uuid));
            futures.add(future);
        }
        List<String> result = new ArrayList<>();
        for (Future<String> future : futures) {
            try {
                result.add(future.get());
            } catch (Exception e) {

            }
        }

        return JSON.toJSONString(result);
    }

    public String buildIconByCf(Document detailDoc) throws Exception {
        String src = detailDoc.select("div.hnnXjf div.qxNhq div.RhBWnf").get(0).select("img.T75of").attr("src");
        String uuid = UUID.randomUUID().toString();
        return r2Utils.uploadToCloudflare(src,uuid);
    }

    public String buildIconByQiniu(Document detailDoc, String token) {
        String src = "";
        Elements elements = detailDoc.select("div.wkMJlb div.Mqg6jb img.T75of");
        if (elements.size() > 0) {
            src = elements.get(0).attr("src");
        }else {
            src = detailDoc.select("div.hnnXjf div.qxNhq div.RhBWnf").get(0).select("img.T75of").attr("src");
        }
        String uuid = UUID.randomUUID().toString();
        try {
            return qiniuUtils.downloadAndUpload(src, uuid, token,3);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String buildBannersByQiniu(Document detailDoc, String token) {
        Elements elements = detailDoc.select("div.ULeU3b").select("img.T75of");

        List<Future<String>> futures = new ArrayList<>();

        for (Element imgElement : elements) {
            String imgHref = imgElement.attr("src");
            String uuid = UUID.randomUUID().toString();
            Future<String> future = excutor.submit(() -> qiniuUtils.downloadAndUpload(imgHref, uuid, token,3));
            futures.add(future);
        }
        List<String> result = new ArrayList<>();
        for (Future<String> future : futures) {
            try {
                result.add(future.get());
            } catch (Exception e) {

            }
        }

        return JSON.toJSONString(result);
    }

    public String buildUpdateDate(Document detailDoc) {
        return detailDoc.select("div.TKjAsc div.xg1aie").first().text();
    }

    public String buildPrice(Document detailDoc, String detail) {
        String price = detailDoc.select("div.u4ICaf span.VfPpkd-vQzf8d").first().text();
        ExtraInfo extraInfo = JSON.parseObject(detail, ExtraInfo.class);
        extraInfo.setPrice(price);
        return JSON.toJSONString(extraInfo);
    }

    public Document getGoogleHtml(String url) {
        Request request = OkHttpClientUtils.buildRequest(url);
        Response response = null;
        try {
            response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute();
            if (response.isSuccessful()) {
                return Jsoup.parse(response.body().string());
            }else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }finally {
            response.close();
        }
    }

    public String getGoogleHtmlTxt(String url) {
        Request request = OkHttpClientUtils.buildRequest(url);
        Response response = null;
        try {
            response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute();
            if (response.isSuccessful()) {
                return response.body().string();
            }else {
                return null;
            }
        } catch (Exception e) {
            return null;
        }finally {
            response.close();
        }
    }

    public String buildAppName(Document detailDoc, String sourceName) {
        Elements elements1 = detailDoc.select("div.qxNhq div.RhBWnf div.Fd93Bb h1 span.AfwdI");
        if (elements1 != null && elements1.size() != 0) {
            return elements1.first().text();
        }
        Elements elements2 = detailDoc.select("div.Fd93Bb h1 span.AfwdI");
        if (elements2 != null && elements2.size() != 0) {
            return elements2.first().text();
        }
        return sourceName;

    }

    public float buildScore(Document detailDoc) {
        try {
            String text = detailDoc.select("div.w7Iutd div.wVqUob div.ClM7O div.TT9eCd").first().text();
            return Float.parseFloat(extractNumber(text));
        }catch (Exception e) {
            return 3.5f;
        }
    }

    public String buildInstalls(Document detailDoc) {
        String installs1 = detailDoc.select("div.wVqUob div.ClM7O").get(1).text();
        if (StringUtils.isBlank(installs1)) {
            return detailDoc.select("div.w7Iutd div.wVqUob div.ClM7O").get(0).text();
        }
        return installs1;
     }

    public String buildEmail(Document detailDoc) {
        Elements elements = detailDoc.select("div.pZ8Djf div.pSEeg");
        if (elements.size() == 2) {
            return elements.get(1).text();
        }
        return elements.first().text();
    }

    private static String extractNumber(String input) {
        Pattern pattern = Pattern.compile("[-+]?\\d*\\.?\\d+");
        Matcher matcher = pattern.matcher(input);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    public String buildLatestVersion(String url) {
        String googleHtmlTxt = getGoogleHtmlTxt(url);
        if (googleHtmlTxt == null) {
            return null;
        }
        String regex = "<script\\s+class=\"ds:5\"\\s+nonce=\"[^\"]*\"[^>]*>(.*?)</script>";
        Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(googleHtmlTxt);

        String versionRegex = "\\b\\d+(?:\\.\\d+){2,3}\\b";
        Pattern versionPattern = Pattern.compile(versionRegex, Pattern.DOTALL);
        String version = "";


        while (matcher.find()) {
            Matcher versionMatcher = versionPattern.matcher(matcher.group());
            if (versionMatcher.find()) {
                version = versionMatcher.group();
            }else {
                version = "Varies with device";
            }
        }

        return version;
    }

    public String buildDescription(Document detailDoc) {
        return detailDoc.select("div.SfzRHd div.bARER").first().toString();
    }

    public String buildVideo(Document detailDoc) {
        if (detailDoc.select("div.kuvzJc button.cvriud ").size() == 0) {
            return "";
        }
        return detailDoc.select("div.kuvzJc button.cvriud ").first().attr("data-trailer-url");
    }

    public String buildDeveloper(Document googleHtml) {
        try {
            return googleHtml.select("div.HhKIQc").select("div").get(1).text();
        }catch (Exception e) {
            return "";
        }

    }

    public void buildApkDataInfo(Document doc, TApkData apkData, String token) {
        String img = buildIconByQiniu(doc, token);
        String banners = buildBannersByQiniu(doc, token);
        String video = buildVideo(doc);
        String updateDate = buildUpdateDate(doc);
        float score = buildScore(doc);
        String installs = buildInstalls(doc);
        String description = buildDescription(doc);

        apkData.setImg(img);
        apkData.setBanners(banners);

        apkData.setUpdateDate(updateDate);
        apkData.setScore(score);
        apkData.setInstalls(installs);
        apkData.setDescription(description);
        apkData.setVideo(video);
    }

    public String buildExtraInfo(String googleUrl, Document doc, String subCategory) {
        String version = buildLatestVersion(googleUrl);
        String email = buildEmail(doc);
        String developer = buildDeveloper(doc);
        ExtraInfo info = new ExtraInfo();
        info.setCategory(subCategory);
        info.setLatestVersion(version);
        info.setEmail(email);
        info.setOfferedBy(developer);
        return JSON.toJSONString(info);
    }

    public String buildSubCategoryFromPac(String bundle) {
        String url = String.format("https://www.apkpac.com/app/%s", bundle);
        Request request = OkHttpClientUtils.buildRequest(url);
        Response response = null;
        try {
            response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute();
            if (response.isSuccessful()) {
                Document doc = Jsoup.parse(response.body().string());
                String text = doc.select(".appdetail-tab").first().select(".appdetail-maintab").first().select(".maintab-item-t").first().text();
                return text.replace(" ", "");
            }
        }catch (Exception e) {

        }
        return "";
    }

}
