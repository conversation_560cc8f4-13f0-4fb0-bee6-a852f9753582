package com.apk.website.schedule;

import com.alibaba.fastjson.JSON;
import com.apk.website.common.exception.BaseException;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.dto.OfferInfoDto;
import com.apk.website.entity.TApkData;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.vo.resp.OfferListRsp;
import com.apk.website.vo.resp.OfferListV2Rsp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 拉取网盟offer
 * @create 2025/5/22
 */
@Slf4j
@Component
public class ApkOfferSchedule {

    private final static String NETWORK_URL = "http://bulk3.batmobi.net/api/network?app_key=3R7RPW306EEJL5OFVWMSTTG3&limit=%s&page=%s";

    private final static String NETWORK_URL_V2 = "https://api.movablead.net/api/offer/list?app_id=T14CM7P3NPJEGAY0H4J4504Y&limit=%s&page=%s";

    @Resource
    private ApkDataMapper mapper;

    @Scheduled(cron = "0 */10 * * * ?")
    public void pulOffer() {
        apiPullOfferV1();
        apiPullOfferV2();
        log.info("apk offer pull end");
    }

    private void apiPullOfferV1() {
        int page = 1, size = 100;
        while (true) {
            String url = String.format(NETWORK_URL, size, page);
            Request offerReq = new Request.Builder().url(url).build();
            try (Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(offerReq).execute()) {
                ResponseBody responseBody = response.body();
                OfferListRsp offerListRsp = JSON.parseObject(responseBody.string(), OfferListRsp.class);
                if (offerListRsp != null && offerListRsp.getStatus() == HttpStatus.SC_OK
                        && CollectionUtils.isNotEmpty(offerListRsp.getOffers())) {
                    log.info("apk站点：本次获取的offer数量：{}", offerListRsp.getOffers().size());
                    offerDataToMysql(offerListRsp);
                    if (page < offerListRsp.getPages()) {
                        page++;
                        TimeUnit.SECONDS.sleep(1);
                    } else {
                        break;
                    }
                } else {
                    log.error("获取到的offer信息为null,result:{}", JSON.toJSONString(offerListRsp));
                    break;
                }
            } catch (Exception e) {
                log.error("获取offer列表信息失败!", e);
                break;
            }
        }
    }

    private void apiPullOfferV2() {
        int page = 1, size = 100;
        while (true) {
            String url = String.format(NETWORK_URL_V2, size, page);
            Request offerReq = new Request.Builder().url(url).build();
            try (Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(offerReq).execute()) {
                ResponseBody responseBody = response.body();
                OfferListV2Rsp offerListRsp = JSON.parseObject(responseBody.string(), OfferListV2Rsp.class);
                if (offerListRsp != null && offerListRsp.getCode() == HttpStatus.SC_OK
                        && CollectionUtils.isNotEmpty(offerListRsp.getOfferList())) {
                    OfferListRsp listRsp = offerListRsp.convertOfferRsp(offerListRsp);
                    log.info("apk站点：本次获取的offer数量：{}", listRsp.getOffers().size());
                    offerDataToMysql(listRsp);
                    if (page < offerListRsp.getPageCount()) {
                        page++;
                        TimeUnit.SECONDS.sleep(1);
                    } else {
                        break;
                    }
                } else {
                    log.error("获取到的offer V2信息为null,result:{}", JSON.toJSONString(offerListRsp));
                    break;
                }
            } catch (Exception e) {
                log.error("获取offer V2列表信息失败!", e);
                throw new BaseException("获取offer V2列表信息失败!");
            }
        }
    }

    private void offerDataToMysql(OfferListRsp offerListRsp) {

        List<OfferInfoDto> offers = offerListRsp.getOffers();

        List<String> bundles = offers.stream().map(offer ->
                offer.getMobileAppId()
        ).collect(Collectors.toList());

        List<TApkData> apkList = mapper.selectList(new LambdaQueryWrapper<>(TApkData.class).in(TApkData::getBundle, bundles));

        Map<String, List<TApkData>> apkMap = apkList.stream()
                .collect(Collectors.groupingBy(TApkData::getBundle));


        for (OfferInfoDto offer : offers) {
            List<TApkData> TApkDataList = apkMap.get(offer.getMobileAppId());
            if (TApkDataList != null) {
                for (TApkData TApkData : TApkDataList) {
                    TApkData.setOfferUrl(offer.getClickUrl());
                    mapper.updateById(TApkData);
                    log.info("更新apk offer url：{}", TApkData.getId());
                }
            }

        }

    }
    
}
