package com.apk.website.schedule;

import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.entity.TApkData;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.schedule.utils.GoogleUtils;
import com.apk.website.schedule.utils.QiniuUtils;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class ApkfunnystoreSchedule {

    @Autowired
    private ApkDataMapper apkDataMapper;

    @Autowired
    private GoogleUtils googleUtils;

    @Autowired
    private QiniuUtils qiniuUtils;
    Set<String> existBundles;
    String category = "Apps";
    public void pull() {
//        ApkfunnystoreSchedule appnstoreSchedule = new ApkfunnystoreSchedule();
        Map<String,String> urls = new HashMap<>();
        urls.put("https://apkfunny.com/en/categories", "Apps");
        urls.put("https://apkfunny.com/en/game","Games");
        for (String url : urls.keySet()){
            category=urls.get(url);
            Request request =buildRequest(url);
            List<TApkData> apkDataList = apkDataMapper.selectList(null);
            existBundles = apkDataList.stream().map(TApkData::getBundle).collect(Collectors.toSet());

            try(Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String htmlText = response.body().string();
                    Document document = Jsoup.parse(htmlText);
                    // 子分类
                    Elements subCategoryElements = document.select(".app-cate .app-cate-item");
                    for (Element subEle : subCategoryElements){
                        //获取分类标签链接
                        Elements a = subEle.select("a");
                        String href = a.attr("href");
                        String subUrl = "https://apkfunny.com" + href;
                        //获取分类标签
                        String subCategory = a.text().trim();

                        //一个分类下多个app
//                    ArrayList<TApkData> tApkDataArrayList = new ArrayList<>();
                        Request subCategoryUrl =buildRequest(subUrl);
                        try(Response subCategoryResp = OkHttpClientUtils.CLIENT.getClientInstance().newCall(subCategoryUrl).execute();) {
                            // 先进去看看有多少个应用
                            if (subCategoryResp.isSuccessful()) {
                                String subCategoryHtmlText = subCategoryResp.body().string();
                                Document subCategoryDocument = Jsoup.parse(subCategoryHtmlText);
                                //获取总数total
                                String total = subCategoryDocument.select(".rank-total .rank-total-span").text().split(" ")[2];
                                int pages = Integer.parseInt(total)/9+1;//?page=1

                                for(int page=1;page<=pages;page++){
                                    appList(subUrl+"?page="+page, subCategory);
                                }

                            }
                        }catch (IOException e){
                            e.printStackTrace();
                        }
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }
    private  void appList(String subUrl, String subCategory) {
        Request subCategoryUrl =buildRequest(subUrl);
        try( Response subCategoryResp = OkHttpClientUtils.CLIENT.getClientInstance().newCall(subCategoryUrl).execute();) {
            // 分类对应的app列表
            if (subCategoryResp.isSuccessful()) {
                String subCategoryHtmlText = subCategoryResp.body().string();
                Document subCategoryDocument = Jsoup.parse(subCategoryHtmlText);
                // app列表
                Elements appElements = subCategoryDocument.select(".category-ls .category-item .category-con");
                for (Element appEle : appElements){
                    //获取app详情的链接
                    String href1 = appEle.select("a").attr("href");
                    String appUrl = "https://apkfunny.com" + href1;
                    //获取包名
                    String[] split = href1.split("/");
                    String bundle = split[split.length - 1].replace("-", ".");

                    // todo 如果存在，跳过
                    if (existBundles.contains(bundle)) {
                        continue;
                    }

                    String googleUrl = String.format("https://play.google.com/store/apps/details?id=%s&hl=en&gl=us", bundle);


                    // todo 如果谷歌下架，跳过
                    Document googleHtml = googleUtils.getGoogleHtml(googleUrl);
                    if (googleHtml ==null) {
                        continue;
                    }

                    TApkData tApkData = new TApkData();
                    String token = qiniuUtils.getQiniuToken();
                    Request detailRequest = buildRequest(appUrl);
                    try(Response detailResponse = OkHttpClientUtils.CLIENT.getClientInstance().newCall(detailRequest).execute();) {
                        if (detailResponse.isSuccessful()) {
                            String detailHtml = detailResponse.body().string();
                            Document detailDoc = Jsoup.parse(detailHtml);
                            String appName = detailDoc.select(".about-app_item .content .tip-name").get(0).text();
                            tApkData.setAppName(appName);
                            tApkData.setBundle(bundle);
                            tApkData.setSubCategory(subCategory);
                            tApkData.setGoogleUrl(googleUrl);
                            tApkData.setCategory(category);
                            tApkData.setLanguage("en");
                            tApkData.setSource("apkfunny");

                            String detail = detailDoc.select("div.app-descript.hiddeninfo").toString();
//                                        String key_features = detailDoc.select("div.key-features").get(0).toString();
//                                        String personalized = detailDoc.select("div.personalized").get(0).toString();
                            tApkData.setDetail(detail);

                            String extraInfo = googleUtils.buildExtraInfo(googleUrl, googleHtml, subCategory);
                            googleUtils.buildApkDataInfo(googleHtml,tApkData, token);

                            tApkData.setExtraInfo(extraInfo);

//                                        tApkDataArrayList.add(tApkData);
                            apkDataMapper.insert(tApkData);
                        }
                    }catch (IOException e){
                        e.printStackTrace();
                    }
                }
            }

        }catch (IOException e){
            e.printStackTrace();
        }

    }
    private Request buildRequest(String url) {
        Request.Builder builder = new Request.Builder().url(url);
        builder.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3");
        builder.addHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
        return builder.build();
    }


}

