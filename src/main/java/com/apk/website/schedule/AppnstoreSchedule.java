package com.apk.website.schedule;

import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.entity.TApkData;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.schedule.utils.GoogleUtils;
import com.apk.website.schedule.utils.QiniuUtils;
import com.qiniu.common.QiniuException;
import okhttp3.*;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class AppnstoreSchedule {

    @Autowired
    private ApkDataMapper apkDataMapper;

    @Autowired
    private GoogleUtils googleUtils;

    @Autowired
    private QiniuUtils qiniuUtils;
    public void pull() {
        AppnstoreSchedule appnstoreSchedule = new AppnstoreSchedule();
        String url = "https://appnstore.com/category/game-simulation.html";
        Request request = appnstoreSchedule.buildRequest(url);

        List<TApkData> apkDataList = apkDataMapper.selectList(null);
        List<String> existBundles = apkDataList.stream().map(TApkData::getBundle).collect(Collectors.toList());

        try {
            Response response = OkHttpClientUtils.CLIENT.getClientInstance().newCall(request).execute();
            if (response.isSuccessful()) {
                String htmlText = response.body().string();
                Document document = Jsoup.parse(htmlText);

                // 子分类
                Elements subCategoryElements = document.select(".app_category .card .card_item");
                for (Element subEle : subCategoryElements){
                    //获取分类标签链接
                    String href = subEle.select("a").attr("href");
                    String subUrl = "https://appnstore.com" + href;
                    //获取分类标签
                    String subCategory = subEle.select("div.text").text();

                    //一个分类下多个app
                    ArrayList<TApkData> tApkDataArrayList = new ArrayList<>();
                    Request subCategoryUrl = appnstoreSchedule.buildRequest(subUrl);
                    try {
                        // 分类对应的app列表
                        Response subCategoryResp = OkHttpClientUtils.CLIENT.getClientInstance().newCall(subCategoryUrl).execute();
                        if (subCategoryResp.isSuccessful()) {
                            String subCategoryHtmlText = subCategoryResp.body().string();
                            Document subCategoryDocument = Jsoup.parse(subCategoryHtmlText);
                            // app列表
                            Elements appElements = subCategoryDocument.select(".category_container .apps .cards .card_item");
                            for (Element appEle : appElements){
                                //获取app详情的链接
                                String href1 = appEle.select("a").attr("href");
                                String appUrl = "https://appnstore.com" + href1;
                                //获取包名
                                String bundle =href1.split("/")[2].substring(0, href1.split("/")[2].length()-5);

                                // todo 如果存在，跳过
                                if (existBundles.contains(bundle)) {
                                    continue;
                                }

                                String googleUrl = String.format("https://play.google.com/store/apps/details?id=%s&hl=en&gl=us", bundle);


                                // todo 如果谷歌下架，跳过
                                Document googleHtml = googleUtils.getGoogleHtml(googleUrl);
                                if (googleHtml ==null) {
                                    continue;
                                }

                                TApkData tApkData = new TApkData();
                                String token = qiniuUtils.getQiniuToken();
                                Request detailRequest = appnstoreSchedule.buildRequest(appUrl);
                                try {
                                    Response detailResponse = OkHttpClientUtils.CLIENT.getClientInstance().newCall(detailRequest).execute();
                                    if (detailResponse.isSuccessful()) {
                                        String detailHtml = detailResponse.body().string();
                                        Document detailDoc = Jsoup.parse(detailHtml);
                                        String appName = detailDoc.select("div.header-right-name").text();
                                        tApkData.setAppName(appName);
                                        tApkData.setBundle(bundle);
                                        tApkData.setSubCategory(subCategory);
                                        tApkData.setGoogleUrl(googleUrl);
                                        tApkData.setCategory("Games");
                                        tApkData.setLanguage("en");
                                        tApkData.setSource("appnstore");

                                        String description = detailDoc.select("div.description").get(0).toString();
                                        String key_features = detailDoc.select("div.key-features").get(0).toString();
                                        String personalized = detailDoc.select("div.personalized").get(0).toString();
                                        tApkData.setDetail(description+"\n"+key_features+"\n"+personalized);

                                        String extraInfo = googleUtils.buildExtraInfo(googleUrl, googleHtml, subCategory);
                                        googleUtils.buildApkDataInfo(googleHtml,tApkData, token);

                                        tApkData.setExtraInfo(extraInfo);

                                        tApkDataArrayList.add(tApkData);
                                        apkDataMapper.insert(tApkData);
                                    }
                                }catch (IOException e){
                                   e.printStackTrace();
                                }
                            }
                        }


                    }catch (IOException e){
                        e.printStackTrace();
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }
    private Request buildRequest(String url) {
        Request.Builder builder = new Request.Builder().url(url);
        builder.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3");
        builder.addHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
        return builder.build();
    }

}

