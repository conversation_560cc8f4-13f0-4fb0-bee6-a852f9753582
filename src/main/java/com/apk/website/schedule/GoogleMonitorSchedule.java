package com.apk.website.schedule;

import com.alibaba.fastjson.JSON;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.constant.RemovedReasonConstant;
import com.apk.website.constant.StatusConstant;
import com.apk.website.entity.TApkData;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.schedule.utils.GoogleUtils;
import com.apk.website.vo.ExtraInfo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 每10天从谷歌商店更新信息
 * @create 2025/5/27
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class GoogleMonitorSchedule {

    private final ApkDataMapper apkDataMapper;
    private final GoogleUtils googleUtils;


//    public void monitor() {
//        log.info("谷歌商店更新信息定时任务开始：{}",  new Date());
////        List<TApkData> apkDataList = apkDataMapper.selectList(
////                new LambdaQueryWrapper<>(TApkData.class).eq(TApkData::getLanguage, "jp").lt(TApkData::getCreateTime, "2025-05-22 10:00:00")
////        );
//        List<TApkData> apkDataList = apkDataMapper.select();
//        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
//
//        for (TApkData apkData : apkDataList) {
//            String googleUrl = apkData.getGoogleUrl();
//            String appleUrl = apkData.getAppleUrl();
//
//            if (StringUtils.isNotBlank(apkData.getApkUrl())) {
//                continue;
//            }
//
//            // 下架逻辑
//            float sourceScore = apkData.getScore();

    /// /            if (sourceScore < 3.5) {
    /// /                apkData.setStatus(StatusConstant.CLOSE.getCode());
    /// /                apkData.setReason(RemovedReasonConstant.SCORE_LT.getCode());
    /// /                apkDataMapper.updateById(apkData);
    /// /                log.info("appId :{} 下架", apkData.getId());
    /// /                continue;
    /// /            }
//
//            boolean appleFlag = false;
//            if (StringUtils.isBlank(appleUrl)) {
//                appleFlag = true;
//            }else {
//                Request appleReq = OkHttpClientUtils.buildRequest(appleUrl);
//                Response appleResp = null;
//                try {
//                    appleResp = httpClient.newCall(appleReq).execute();
//                } catch (IOException e) {
//
//                }
//                if (!appleResp.isSuccessful()) {
//                    appleFlag = true;
//                }
//
//            }
//
//            // 更新信息
//            if (StringUtils.isNotBlank(googleUrl)) {
//                Request googleReq = OkHttpClientUtils.buildRequest(googleUrl);
//                Response googleResp = null;
//                try {
//                    googleResp = httpClient.newCall(googleReq).execute();
//                    if (!googleResp.isSuccessful() && appleFlag) {
//                        apkData.setStatus(StatusConstant.CLOSE.getCode());
//                        apkData.setReason(RemovedReasonConstant.URL_INVALID.getCode());
//                        apkDataMapper.updateById(apkData);
//                        log.info("appId :{} 下架", apkData.getId());
//                        continue;
//                    }
//
//                    Document googleHtml = googleUtils.getGoogleHtml(googleUrl);
//                    if (googleHtml == null) {
//                        continue;
//                    }
//                    String appName = googleUtils.buildAppName(googleHtml, apkData.getAppName());
//                    float score = googleUtils.buildScore(googleHtml);
//                    String installs = googleUtils.buildInstalls(googleHtml);
//                    String updateDate = googleUtils.buildUpdateDate(googleHtml);
//                    String description = googleUtils.buildDescription(googleHtml);
//                    String video = googleUtils.buildVideo(googleHtml);
//                    buildExtraInfo(apkData.getExtraInfo(), googleHtml, googleUrl);
//
//                    apkData.setAppName(appName);
//                    apkData.setScore(score);
//                    apkData.setInstalls(installs);
//                    apkData.setUpdateDate(updateDate);
//                    apkData.setDescription(description);
//                    apkData.setExtraInfo(buildExtraInfo(apkData.getExtraInfo(), googleHtml, googleUrl));
//                    apkData.setVideo(video);
//                    apkData.setStatus(StatusConstant.OPEN.getCode());
//                    apkData.setReason(RemovedReasonConstant.NORMAL.getCode());
//
//                    apkDataMapper.updateById(apkData);
//
//                }catch (Exception e) {
//                    log.error("谷歌商店更新信息定时任务异常：{}, apk_Id: {}", e, apkData.getId());
//                }finally {
//                    googleResp.close();
//                }
//            }
//        }
//
//    }
    @Scheduled(cron = "0 0 9 * * Mon")
    public void monitor() {
        log.info("谷歌商店更新信息定时任务开始：{}", new Date());

        List<TApkData> apkDataList = apkDataMapper.selectList(new LambdaQueryWrapper<>(TApkData.class).eq(TApkData::getSubCategory, "Finance").eq(TApkData::getLanguage, "en"));
        if (CollectionUtils.isEmpty(apkDataList)) {
            return;
        }

        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        int threadSize = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(threadSize);
        CountDownLatch latch = new CountDownLatch(apkDataList.size());

        for (TApkData apkData : apkDataList) {
            executorService.submit(() -> {
                try {
                    String googleUrl = apkData.getGoogleUrl();
                    String appleUrl = apkData.getAppleUrl();

                    if (StringUtils.isNotBlank(apkData.getApkUrl())) {
                        return;
                    }

                    // 下架逻辑
                    float sourceScore = apkData.getScore();
                    // if (sourceScore < 3.5) {
                    //     apkData.setStatus(StatusConstant.CLOSE.getCode());
                    //     apkData.setReason(RemovedReasonConstant.SCORE_LT.getCode());
                    //     apkDataMapper.updateById(apkData);
                    //     log.info("appId :{} 下架", apkData.getId());
                    //     return;
                    // }

                    boolean appleFlag = false;
                    if (StringUtils.isBlank(appleUrl)) {
                        appleFlag = true;
                    } else {
                        Request appleReq = OkHttpClientUtils.buildRequest(appleUrl);
                        try (Response appleResp = httpClient.newCall(appleReq).execute()) {
                            if (!appleResp.isSuccessful()) {
                                appleFlag = true;
                            }
                        } catch (IOException ignored) {
                            appleFlag = true;
                        }
                    }

                    if (StringUtils.isNotBlank(googleUrl)) {
                        Request googleReq = OkHttpClientUtils.buildRequest(googleUrl);
                        try (Response googleResp = httpClient.newCall(googleReq).execute()) {
                            if (!googleResp.isSuccessful() && appleFlag) {
                                apkData.setStatus(StatusConstant.CLOSE.getCode());
                                apkData.setReason(RemovedReasonConstant.URL_INVALID.getCode());
                                apkDataMapper.updateById(apkData);
                                log.info("appId :{} 下架", apkData.getId());
                                return;
                            }

                            Document googleHtml = googleUtils.getGoogleHtml(googleUrl);
                            if (googleHtml == null) {
                                return;
                            }

                            apkData.setAppName(googleUtils.buildAppName(googleHtml, apkData.getAppName()));
                            apkData.setScore(googleUtils.buildScore(googleHtml));
                            apkData.setInstalls(googleUtils.buildInstalls(googleHtml));
                            apkData.setUpdateDate(googleUtils.buildUpdateDate(googleHtml));
                            apkData.setDescription(googleUtils.buildDescription(googleHtml));
                            apkData.setExtraInfo(buildExtraInfo(apkData.getExtraInfo(), googleHtml, googleUrl));
                            apkData.setVideo(googleUtils.buildVideo(googleHtml));
                            apkData.setStatus(StatusConstant.OPEN.getCode());
                            apkData.setReason(RemovedReasonConstant.NORMAL.getCode());

                            apkDataMapper.updateById(apkData);
                            log.info("{} 更新完成", apkData.getId());
                        }
                    }

                } catch (Exception e) {
                    log.error("处理异常，apkId: {}, 异常信息：", apkData.getId(), e);
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await(); // 等待所有任务完成
        } catch (InterruptedException e) {
            log.error("线程等待被中断：", e);
            Thread.currentThread().interrupt();
        } finally {
            executorService.shutdown();
        }

        log.info("谷歌商店更新信息定时任务结束：{}", new Date());
    }


    private String buildExtraInfo(String sourceInfo, Document googleHtml, String googleUrl) {
        ExtraInfo info = JSON.parseObject(sourceInfo, ExtraInfo.class);
        String version = googleUtils.buildLatestVersion(googleUrl);
        String developer = googleUtils.buildDeveloper(googleHtml);
        String email = googleUtils.buildEmail(googleHtml);
        info.setLatestVersion(version);
        info.setOfferedBy(developer);
        info.setEmail(email);

        return JSON.toJSONString(info);
    }

}
