package com.apk.website.schedule;

import com.alibaba.druid.pool.WrapperAdapter;
import com.apk.website.entity.*;
import com.apk.website.mapper.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 定期上架apk和article
 * @create 2025/5/19
 */
@Component
@Slf4j
@AllArgsConstructor
public class RelationSchedule {

    private final ApkDataMapper apkDataMapper;
    private final ApkArticleMapper apkArticleMapper;
    private final ApkDomainRelationMapper apkDomainRelationMapper;
    private final ArticleDomainRelationMapper articleDomainRelationMapper;
    private final DomainMapper domainMapper;

    @Scheduled(cron = "0 0 0 * * ?")
    public void relation() {
        List<TDomain> domains = domainMapper.selectList(null);
        for (TDomain domain : domains) {
            Integer domainId = domain.getId();
            relationApk(domainId);
            relationArticle(domainId);
        }
    }

    private void relationApk(Integer domainId) {
        LambdaQueryWrapper<TApkDomainRelation> wrapper = new LambdaQueryWrapper<>(TApkDomainRelation.class);
        wrapper.eq(TApkDomainRelation::getDomainId, domainId);
        List<TApkDomainRelation> relationList = apkDomainRelationMapper.selectList(wrapper);
        List<Long> existApkIds = relationList.stream().map(TApkDomainRelation::getApkId).collect(Collectors.toList());

        LambdaQueryWrapper<TApkData> apkWrapper = new LambdaQueryWrapper<>(TApkData.class);
        apkWrapper.notIn(TApkData::getId, existApkIds);
        List<TApkData> apkDataList = apkDataMapper.selectList(apkWrapper);
        Collections.shuffle(apkDataList);
        List<TApkData> bindApks = apkDataList.subList(0, 3);

        for (TApkData bindApk : bindApks) {
            TApkDomainRelation relation = new TApkDomainRelation();
            relation.setDomainId(domainId);
            relation.setApkId(bindApk.getId());
            apkDomainRelationMapper.insert(relation);
        }
        log.info("站点id: {} 应用关联成功",domainId);
    }

    private void relationArticle(Integer domainId) {
        LambdaQueryWrapper<TArticleDomainRelation> wrapper = new LambdaQueryWrapper<>(TArticleDomainRelation.class);
        wrapper.eq(TArticleDomainRelation::getDomainId, domainId);
        List<TArticleDomainRelation> relationList = articleDomainRelationMapper.selectList(wrapper);
        List<Integer> existArticleIds = relationList.stream().map(TArticleDomainRelation::getArticleId).collect(Collectors.toList());
        LambdaQueryWrapper<TApkArticle> apkWrapper = new LambdaQueryWrapper<>(TApkArticle.class);
        apkWrapper.notIn(TApkArticle::getId, existArticleIds);
        List<TApkArticle> apkDataList = apkArticleMapper.selectList(apkWrapper);
        Collections.shuffle(apkDataList);
        List<TApkArticle> bindApks = apkDataList.subList(0, 1);
        for (TApkArticle bindApk : bindApks) {
            TArticleDomainRelation relation = new TArticleDomainRelation();
            relation.setDomainId(domainId);
            relation.setArticleId(bindApk.getId());
            articleDomainRelationMapper.insert(relation);
        }
        log.info("站点id: {} 文章关联成功",domainId);
    }



}
