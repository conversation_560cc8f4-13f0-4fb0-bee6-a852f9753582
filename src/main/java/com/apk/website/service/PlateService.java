package com.apk.website.service;

import com.apk.website.common.utils.PageUtils;
import com.apk.website.common.vo.PageData;
import com.apk.website.dto.admin.plateReq;
import com.apk.website.entity.TPlate;
import com.apk.website.mapper.PlateMapper;
import com.apk.website.vo.CategoryTreeNode;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlateService {

    private final PlateMapper plateMapper;

    public PageData list(plateReq req) {
        LambdaQueryWrapper<TPlate> queryWrapper = new LambdaQueryWrapper<>(TPlate.class);
        if (req.getId() != null) {
            queryWrapper.eq(TPlate::getId, req.getId());
        }
        if (req.getParentId() != null) {
            queryWrapper.eq(TPlate::getParentId, req.getParentId());
        }
        if (req.getStatus() != null) {
            queryWrapper.eq(TPlate::getStatus, req.getStatus());
        }
        if (req.getLevel() != null) {
            queryWrapper.eq(TPlate::getLevel, req.getLevel());
        }
        Page page = new Page<>(req.getPageNum(),req.getPageSize());
        IPage iPage = plateMapper.selectPage(page, queryWrapper);
        return PageUtils.coverPage(iPage);
    }

    public int add(plateReq req) {
        TPlate tPlate = new TPlate();
        BeanUtils.copyProperties(req, tPlate);
        return plateMapper.insert(tPlate);
    }

    public int delete(plateReq req) {
        return plateMapper.deleteBatchIds(req.getIds());
    }

    public int update(plateReq req) {
        TPlate tPlate = new TPlate();
        BeanUtils.copyProperties(req, tPlate);
        return plateMapper.updateById(tPlate);
    }

    public TPlate getOne(plateReq req) {
        return plateMapper.selectById(req.getId());
    }

//    public List<CategoryTreeNode> getOneTree(WebConfigReq req) {
//        LambdaQueryWrapper<TPlate> queryWrapper = new LambdaQueryWrapper<>(TPlate.class);
//        queryWrapper.eq(TPlate::getParentId, req.getId());
//        queryWrapper.or().eq(TPlate::getId, req.getId());
//        List<TPlate> list = plateMapper.selectList(queryWrapper);
//        return buildTree(list);
//    }
//
//    private List<CategoryTreeNode> buildTree(List<TPlate> list) {
//        List<TPlate> sortedList = list.stream()
//                .collect(Collectors.toList());
//
//        Map<Integer, CategoryTreeNode> nodeMap = new HashMap<>();
//        for (TPlate config : sortedList) {
//            CategoryTreeNode node = new CategoryTreeNode();
//            node.setId(config.getId());
//            node.setParentId(config.getParentId());
//            node.setName(config.getName());
//
//            nodeMap.put(node.getId(), node);
//        }
//
//        List<CategoryTreeNode> tree = new ArrayList<>();
//        for (CategoryTreeNode node : nodeMap.values()) {
//            if (node.getParentId() == 0) {
//                tree.add(node);
//            } else {
//                CategoryTreeNode parentNode = nodeMap.get(node.getParentId());
//                if (parentNode != null) {
//                    parentNode.getChildren().add(node);
//                }
//            }
//        }
//
//        return tree;
//    }

    public List<CategoryTreeNode> getOneTree(plateReq req) {
        // 获取所有的TPlate节点
        LambdaQueryWrapper<TPlate> queryWrapper = new LambdaQueryWrapper<>(TPlate.class);
        List<TPlate> list = plateMapper.selectList(queryWrapper);

        // 将所有节点构建成树形结构
        List<CategoryTreeNode> allNodes = buildTree(list);

        // 找到对应id的子树并返回
        return findTreeById(allNodes, req.getId());
    }

    private List<CategoryTreeNode> buildTree(List<TPlate> list) {
        // 创建Map，方便通过id快速查找节点
        Map<Integer, CategoryTreeNode> nodeMap = new HashMap<>();
        for (TPlate config : list) {
            CategoryTreeNode node = new CategoryTreeNode();
            node.setId(config.getId());
            node.setParentId(config.getParentId());
            node.setName(config.getName());
            node.setType(config.getType());
            node.setChildren(new ArrayList<>());
            nodeMap.put(node.getId(), node);
        }

        // 将节点按父子关系连接起来
        List<CategoryTreeNode> tree = new ArrayList<>();
        for (CategoryTreeNode node : nodeMap.values()) {
            if (node.getParentId() == 0) {
                // 父节点直接加入树形结构
                tree.add(node);
            } else {
                // 非根节点作为子节点加入父节点的children列表
                CategoryTreeNode parentNode = nodeMap.get(node.getParentId());
                if (parentNode != null) {
                    parentNode.getChildren().add(node);
                }
            }
        }

        return tree;
    }

    private List<CategoryTreeNode> findTreeById(List<CategoryTreeNode> tree, int id) {
        for (CategoryTreeNode node : tree) {
            if (node.getId() == id) {
                return Collections.singletonList(node); // 返回匹配id的树
            } else {
                // 递归查找子树
                List<CategoryTreeNode> childrenTree = findTreeById(node.getChildren(), id);
                if (!childrenTree.isEmpty()) {
                    return childrenTree; // 找到子树后返回
                }
            }
        }
        return Collections.emptyList(); // 如果没有找到返回空列表
    }



    public PageData<TPlate> getFirstModule(plateReq req) {
        LambdaQueryWrapper<TPlate> queryWrapper = new LambdaQueryWrapper<>(TPlate.class);
        queryWrapper.eq(TPlate::getParentId, 0);
        IPage page = new Page<>(req.getPageNum(),req.getPageSize());
        IPage iPage = plateMapper.selectPage(page, queryWrapper);
        return PageUtils.coverPage(iPage);

    }
}
