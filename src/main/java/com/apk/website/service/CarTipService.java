package com.apk.website.service;

import com.apk.website.entity.TCarTip;
import com.apk.website.vo.BaseResponse;
import com.apk.website.vo.resp.CarTipInfoResp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.apk.website.mapper.TCarTipMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_car_tip】的数据库操作Service实现
* @createDate 2025-06-05 09:37:25
*/
@Service
public class CarTipService extends ServiceImpl<TCarTipMapper, TCarTip>
    implements IService<TCarTip> {
    @Autowired
    private TCarTipMapper carTipMapper;

    public BaseResponse list(String subCategory) {
        LambdaQueryWrapper<TCarTip> eq = new LambdaQueryWrapper<TCarTip>().eq(StringUtils.hasText(subCategory), TCarTip::getSubCategory, subCategory);
        List<TCarTip> list = baseMapper.selectList(eq);
        return BaseResponse.success(list);
    }

    public BaseResponse getOneTip(Long id) {
        TCarTip carTip = baseMapper.selectById(id);
        List<TCarTip> relates = carTipMapper.listSideTips( 6);
        CarTipInfoResp carTipInfoResp = new CarTipInfoResp();
        BeanUtils.copyProperties(carTip, carTipInfoResp);
        carTipInfoResp.setRelatedBlogs(relates);
        TCarTip next = baseMapper.selectById(id + 1);
        if (next == null) {
            carTipInfoResp.setNextId(null);
        }else {
            carTipInfoResp.setNextId(next.getId());
        }
        return BaseResponse.success(carTipInfoResp);
    }

    public BaseResponse listSideTips(Integer nums) {
        //随机取4条
        if (nums == null||  nums < 1){
            nums = 4;
        }
        List<TCarTip> list = baseMapper.selectList(new LambdaQueryWrapper<TCarTip>().last("ORDER BY RAND() LIMIT 4"));
        return BaseResponse.success(carTipMapper.listSideTips(nums));
    }
}




