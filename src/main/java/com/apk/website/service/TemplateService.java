package com.apk.website.service;

import com.apk.website.common.utils.PageUtils;
import com.apk.website.common.vo.PageData;
import com.apk.website.dto.admin.TemplateReq;
import com.apk.website.entity.TTemplate;
import com.apk.website.mapper.TemplateMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/8
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplateService {

    private final TemplateMapper templateMapper;

    public PageData<TTemplate> list(TemplateReq req) {
        LambdaQueryWrapper<TTemplate> wrapper = new LambdaQueryWrapper<>(TTemplate.class);
        if (StringUtils.isNotBlank(req.getPageType())) {
            wrapper.like(TTemplate::getPageType , req.getPageType());
        }
        if (StringUtils.isNotBlank(req.getName())) {
            wrapper.like(TTemplate::getName , req.getName());
        }

        IPage page = new Page(req.getPageNum(), req.getPageSize());
        IPage<TTemplate> ipage = templateMapper.selectPage(page, wrapper);
        return PageUtils.coverPage(ipage);
    }

    public int add(TemplateReq req) {
        TTemplate dao = new TTemplate();
        BeanUtils.copyProperties(req,dao);
        return templateMapper.insert(dao);
    }

    public int delete(TemplateReq req) {
        return templateMapper.deleteBatchIds(req.getIds());
    }

    public int update(TemplateReq req) {
        TTemplate dao = new TTemplate();
        BeanUtils.copyProperties(req,dao);
        return templateMapper.updateById(dao);
    }

    public TTemplate getOne(TemplateReq req) {
        return templateMapper.selectById(req.getId());
    }
}
