package com.apk.website.service;

import com.apk.website.common.utils.PageUtils;
import com.apk.website.common.vo.PageData;
import com.apk.website.dto.admin.ApkRelationAddReq;
import com.apk.website.dto.admin.ApkRelationReq;
import com.apk.website.entity.TApkData;
import com.apk.website.entity.TApkDomainRelation;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.mapper.ApkDomainRelationMapper;
import com.apk.website.vo.ApkInfoVo;
import com.apk.website.vo.resp.ApkDetailResp;
import com.apk.website.vo.resp.ApkRelationResp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.apk.website.entity.TApkRelation;
import com.apk.website.mapper.TApkRelationMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
*
* @description 针对表【t_apk_relation】的数据库操作Service实现
* @createDate 2025-05-26 10:18:20
*/
@Service
@RequiredArgsConstructor
public class ApkRelationService {
    private final TApkRelationMapper apkRelationMapper;
    private final ApkDataMapper apkDataMapper;
    private final ApkDomainRelationMapper apkDomainRelationMapper;

    public PageData<ApkInfoVo> getApkRelations(ApkRelationReq req) {
        LambdaQueryWrapper<TApkRelation> eq = new LambdaQueryWrapper<TApkRelation>()
                .eq(req.getApkId() != null,TApkRelation::getApkId, req.getApkId())
                .eq(req.getType() != null,TApkRelation::getType, req.getType())
                .eq(req.getDomainId() != null,TApkRelation::getDomainId, req.getDomainId());
        Page<TApkRelation> tApkRelationPage = apkRelationMapper.selectDistinctPage(Page.of(req.getPageNum(), req.getPageSize()), eq);
        List<TApkRelation> records = tApkRelationPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return PageUtils.coverPage(new Page<>(req.getPageNum(), req.getPageSize(), 0));
        }
        List<ApkInfoVo> apkInfoVos = records.parallelStream().map(tApkRelation -> {
            TApkData tApkData = apkDataMapper.selectById(tApkRelation.getApkId());
            ApkInfoVo apkInfoVo = new ApkInfoVo();
            if (tApkData != null){
                BeanUtils.copyProperties(tApkData, apkInfoVo);
            }
//            apkInfoVo.setId(tApkRelation.getId());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            apkInfoVo.setUpdateTime(sdf.format(tApkRelation.getUpdateTime()));
            return apkInfoVo;
        }).collect(Collectors.toList());
        PageData<ApkInfoVo> pageData = new PageData<>();
        pageData.setPageNum((int) tApkRelationPage.getCurrent()).setPages((int) tApkRelationPage.getPages()).setList(apkInfoVos).setTotal(tApkRelationPage.getTotal()).setPageSize(req.getPageSize());
        return pageData;
    }
    public  List<TApkRelation> getApkRelationByApkIdAndType(Long apkId, Integer type) {
        if (apkId == null) {
            throw new RuntimeException("apkId不能为空");
        }
        LambdaQueryWrapper<TApkRelation> eq = new LambdaQueryWrapper<TApkRelation>()
                .eq(TApkRelation::getApkId, apkId)
                .eq(type  != null,TApkRelation::getType, type);
        List<TApkRelation> tApkRelations = apkRelationMapper.selectList(eq);
        return tApkRelations==null? Collections.emptyList():tApkRelations;
    }
    public  List<Long> getApkIdsByApkIdAndType(Long apkId, Integer type,  Integer domainId) {
        if (apkId == null ) {
            throw new RuntimeException("apkId不能为空");
        }
        if (type == null ) {
            throw new RuntimeException("type不能为空");
        }
        if (domainId == null ) {
            throw new RuntimeException("domainId不能为空");
        }
        LambdaQueryWrapper<TApkRelation> eq = new LambdaQueryWrapper<TApkRelation>()
                .eq(TApkRelation::getApkId, apkId)
                .eq(TApkRelation::getDomainId, domainId)
                .eq(TApkRelation::getType, type);
        List<String> tApkRelations = apkRelationMapper.selectListId(eq);
        if (CollectionUtils.isEmpty(tApkRelations)){
            return Collections.emptyList();
        }
        String ids = tApkRelations.get(0);
        return getLongIds(ids);
    }
    private List<Long> getLongIds(String ids){
        String[] split = ids.split(",");
        ArrayList<Long> longIds = new ArrayList<>();
        try {
            for (String s : split) {
                longIds.add(Long.valueOf(s));
            }
        } catch (Exception e) {
            throw new RuntimeException("relationApks解析错误");
        }
        return longIds;
    }
   //更新
    public int updateApkRelation(ApkRelationReq apkRelationReq) {
        if (apkRelationReq == null || apkRelationReq.getId() == null) {
            throw new RuntimeException("参数id不能为空");
        }
        TApkRelation tApkRelation = new TApkRelation();
        BeanUtils.copyProperties(apkRelationReq, tApkRelation);
        tApkRelation.setUpdateTime(new Date());
        return apkRelationMapper.updateById(tApkRelation);
    }
    public int updateByApkIdAndDomainIdAndType(ApkRelationReq apkRelationReq) {
        if (apkRelationReq == null || apkRelationReq.getApkId() == null||  apkRelationReq.getDomainId() == null|| apkRelationReq.getType() == null) {
            throw new RuntimeException("参数不能为空");
        }
        TApkRelation tApkRelation = new TApkRelation();
        BeanUtils.copyProperties(apkRelationReq, tApkRelation);
        tApkRelation.setUpdateTime(new Date());
        LambdaQueryWrapper<TApkRelation> eq = new LambdaQueryWrapper<TApkRelation>()
                .eq(TApkRelation::getApkId, apkRelationReq.getApkId())
                .eq(TApkRelation::getDomainId, apkRelationReq.getDomainId())
                .eq(TApkRelation::getType, apkRelationReq.getType());
        return apkRelationMapper.update(tApkRelation, eq);
    }
    //添加
    @Transactional
    public int addApkRelation(ApkRelationAddReq apkRelationAddReq) {
        if (apkRelationAddReq == null || CollectionUtils.isEmpty(apkRelationAddReq.getApkRelationReqs())) {
            throw new RuntimeException("参数不能为空");
        }
        //删除关联apk
        List<ApkRelationReq> apkRelationReqs = apkRelationAddReq.getApkRelationReqs();
        ApkRelationReq apkRelationReq = apkRelationReqs.get(0);
        LambdaQueryWrapper<TApkRelation> wrapper =new LambdaQueryWrapper<TApkRelation>()
                .eq(TApkRelation::getApkId, apkRelationReq.getApkId())
                .eq(TApkRelation::getDomainId, apkRelationReq.getDomainId());
        int delete = apkRelationMapper.delete(wrapper);

        // 插入apk_domain_relation表
        Long appId = apkRelationReq.getApkId();
        TApkDomainRelation mainRelation = new TApkDomainRelation();
        mainRelation.setDomainId(apkRelationReq.getDomainId());
        mainRelation.setApkId(appId);
        try {
            apkDomainRelationMapper.insert(mainRelation);
        }catch (DuplicateKeyException e) {

        }
        //插入关联apk
        List<TApkRelation> tApkRelations = new ArrayList<>();
        for(ApkRelationReq req : apkRelationReqs){
            if(StringUtils.hasText(req.getRelationApks())){
                //关联的domain表也需要插入
                String[] split = req.getRelationApks().split(",");
                for (String apkId : split) {
                    TApkDomainRelation relation = new TApkDomainRelation();
                    relation.setDomainId(req.getDomainId());
                    relation.setApkId(Long.valueOf(apkId));
                    try {
                        apkDomainRelationMapper.insert(relation);
                    }catch (DuplicateKeyException e) {
                    }
                }
                TApkRelation tApkRelation = new TApkRelation();
                BeanUtils.copyProperties(req, tApkRelation);
                tApkRelation.setId(null);
                tApkRelations.add(tApkRelation);
            }

        }
        int add=0;
        if (!CollectionUtils.isEmpty(tApkRelations)){
            add=apkRelationMapper.batchInsert(tApkRelations);
        }
        return add;
    }
    private  LambdaQueryWrapper<TApkRelation> getWrapper(ApkRelationReq req){
        if (req == null || req.getApkId()== null || req.getDomainId()==null||  req.getType()==null ) {
            throw new RuntimeException("参数不能为空");
        }
        return new LambdaQueryWrapper<TApkRelation>()
                .eq(TApkRelation::getApkId, req.getApkId())
                .eq(TApkRelation::getDomainId, req.getDomainId())
                .eq(TApkRelation::getType, req.getType());
    }
    public ApkRelationResp getOneApkRelation(ApkRelationReq req) {
        LambdaQueryWrapper<TApkRelation> eq = getWrapper(req);
        TApkRelation tApkRelation = apkRelationMapper.selectOne(eq);
        if (tApkRelation == null){
            return null;
        }
        Long apkId = tApkRelation.getApkId();
        ApkRelationResp apkRelationResp = new ApkRelationResp();
        TApkData tApkData = apkDataMapper.selectById(apkId);
        if (tApkData == null){
            return null;
        }
        BeanUtils.copyProperties(tApkData, apkRelationResp);
        apkRelationResp.setApkId(tApkData.getId());
        String relationApks = tApkRelation.getRelationApks();
        if (StringUtils.isEmpty(relationApks)) {
            return null;
        }
        List<Long> apkIds = getLongIds(relationApks);
        List<TApkData> apkDataList = apkDataMapper.selectBatchIds(apkIds);
        //修改顺序和apkIds的顺序一致
        apkDataList.sort(Comparator.comparingInt(apkData -> apkIds.indexOf(apkData.getId())));
        List<ApkDetailResp.RecommendApp> collect = apkDataList.parallelStream().map(apkData -> {
            ApkDetailResp.RecommendApp recommendApp = new ApkDetailResp.RecommendApp();
            BeanUtils.copyProperties(apkData, recommendApp);
            return recommendApp;
        }).collect(Collectors.toList());
        apkRelationResp.setRelationApks(collect);
        apkRelationResp.setUpdateTime(tApkRelation.getUpdateTime());
        return apkRelationResp;
    }

}




