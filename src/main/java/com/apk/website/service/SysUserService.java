package com.apk.website.service;

import com.apk.website.common.exception.BaseException;
import com.apk.website.constant.ResponseCodeConstant;
import com.apk.website.dto.admin.SysUserInfo;
import com.apk.website.entity.TSysUser;
import com.apk.website.mapper.SysUserMapper;
import com.apk.website.common.utils.JwtTokenHelperUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserService {

    private final String ldapDomainName = "@batm" + "obi.org";

    private final SysUserMapper sysUserMapper;

    private final LdapTemplate ldapTemplate;

    public static final int ACCESS_EXPIRE = 60*60*24*7;

    public SysUserInfo loginByLdap(String username, String password) {
        String userDn = username + ldapDomainName;
        try {
            ldapTemplate.getContextSource().getContext(userDn,password);
        } catch (Exception e) {
            log.error("login fail errMsg:{}", e.getMessage(), e);
            throw new BaseException(ResponseCodeConstant.LOGIN_FAIL.getCode(), ResponseCodeConstant.LOGIN_FAIL.getMsg());
        }
        TSysUser sysUser = new LambdaQueryChainWrapper<>(sysUserMapper)
                .eq(TSysUser::getUsername, username).one();
        if(sysUser==null){
            throw new BaseException(ResponseCodeConstant.UNAUTHORIZED.getCode(), ResponseCodeConstant.UNAUTHORIZED.getMsg());
        }
        return genSysUserInfo(sysUser);
    }

    private SysUserInfo genSysUserInfo(TSysUser sysUser){

        SysUserInfo sysUserInfo = new SysUserInfo().setId(sysUser.getId())
                .setUsername(sysUser.getUsername()).setNickname(sysUser.getNickname());

        Date exprireDate = Date.from(Instant.now().plusSeconds(ACCESS_EXPIRE));
        String token = JwtTokenHelperUtils.genAccessToken(sysUserInfo,exprireDate);
        return sysUserInfo.setToken(token).setExprireDate(exprireDate);
    }


}
