package com.apk.website.service;

import com.apk.website.common.exception.BaseException;
import com.apk.website.dto.admin.ApkDomainRelationReq;
import com.apk.website.entity.TApkData;
import com.apk.website.entity.TApkDomainRelation;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.mapper.ApkDomainRelationMapper;
import com.apk.website.vo.ApkInfoVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApkDomainRelationService {

    private final ApkDomainRelationMapper apkDomainRelationMapper;

    private final ApkDataMapper apkDataMapper;

    public List<ApkInfoVo> list(ApkDomainRelationReq req) {
        if (req.getDomainId() == null) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"请选择域名！");
        }

        LambdaQueryWrapper<TApkDomainRelation> queryWrapper = new LambdaQueryWrapper<>(TApkDomainRelation.class);
        queryWrapper.eq(TApkDomainRelation::getDomainId, req.getDomainId());
        if (req.getStatus() != null) {
            queryWrapper.eq(TApkDomainRelation::getStatus, req.getStatus());
        }

        IPage page = new Page(req.getPageNum(), req.getPageSize());
        IPage iPage = apkDomainRelationMapper.selectPage(page, queryWrapper);
        List<TApkDomainRelation> list = iPage.getRecords();

        if (list == null || list.isEmpty()) {
            return null;
        }

        List<Long> apkIds = list.stream().map(TApkDomainRelation::getApkId).collect(Collectors.toList());
        List<TApkData> apps = apkDataMapper.selectBatchIds(apkIds);
        return apps.stream().map(tApkData -> {
            ApkInfoVo vo = new ApkInfoVo();
            BeanUtils.copyProperties(tApkData, vo);
            return vo;
        }).collect(Collectors.toList());

    }

    public int batchAdd(ApkDomainRelationReq req) {
        List<Long> apkIds = req.getApkIds();
        if (apkIds == null || apkIds.isEmpty()) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"请选择apk！");
        }
        if (req.getDomainId() == null) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"请选择域名！");
        }
        int count = 0;
        for (Long apkId : apkIds) {
            TApkDomainRelation apkDomainRelation = new TApkDomainRelation();
            apkDomainRelation.setApkId(apkId);
            apkDomainRelation.setDomainId(req.getDomainId());
            apkDomainRelation.setStatus(0);
            apkDomainRelation.setSource("admin");
            count += apkDomainRelationMapper.insert(apkDomainRelation);
        }

        return count;
    }

    public int batchDel(ApkDomainRelationReq req) {
        return apkDomainRelationMapper.deleteBatchIds(req.getIds());
    }

    public List<ApkInfoVo> unbindApps(ApkDomainRelationReq req) {
        if (req.getDomainId() == null) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"请选择域名！");
        }
        // 已经绑定的
        LambdaQueryWrapper<TApkDomainRelation> queryWrapper = new LambdaQueryWrapper<>(TApkDomainRelation.class);
        queryWrapper.eq(TApkDomainRelation::getDomainId, req.getDomainId());
        List<TApkDomainRelation> bindApps = apkDomainRelationMapper.selectList(queryWrapper);
        List<Long> apkIds = bindApps.stream().map(TApkDomainRelation::getApkId).collect(Collectors.toList());

        IPage page = new Page(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<TApkData> apkQueryWrapper = new LambdaQueryWrapper<>(TApkData.class);
        if(apkIds != null && !apkIds.isEmpty()) {
            apkQueryWrapper.notIn(TApkData::getId, apkIds);
        }
        apkQueryWrapper.eq(TApkData::getLanguage, req.getLanguage());
        List<TApkData> apps = apkDataMapper.selectList(page, apkQueryWrapper);

        return apps.stream().map(tApkData -> {
            ApkInfoVo vo = new ApkInfoVo();
            BeanUtils.copyProperties(tApkData, vo);
            return vo;
        }).collect(Collectors.toList());
    }

}
