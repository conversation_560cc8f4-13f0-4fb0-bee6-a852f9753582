package com.apk.website.service;

import com.apk.website.common.utils.PageUtils;
import com.apk.website.common.vo.PageData;
import com.apk.website.dto.gameshub.GamesHubDetailReq;
import com.apk.website.dto.gameshub.GamesHubHotReq;
import com.apk.website.dto.gameshub.GamesHubReq;
import com.apk.website.dto.gameshub.GamesHubSearchReq;
import com.apk.website.entity.GGamesHub;
import com.apk.website.mapper.GGamesHubMapper;
import com.apk.website.vo.GamesHubDetailVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class GamesHubService {

    private final GGamesHubMapper mapper;

    public PageData<GGamesHub> list(GamesHubReq req) {
        LambdaQueryWrapper<GGamesHub> wrapper = new LambdaQueryWrapper<>(GGamesHub.class);
        wrapper.eq(req.getSeriesId() != null , GGamesHub::getSeriesId, req.getSeriesId()).eq(req.getLang() != null, GGamesHub::getLang, req.getLang());

        IPage<GGamesHub> page = new Page<>(req.getPageNum(), req.getPageSize());

        IPage<GGamesHub> iPage = mapper.selectPage(page, wrapper);

        return PageUtils.coverPage(iPage);
    }

    public List<GGamesHub> hot (GamesHubHotReq req) {
        return mapper.selectViewRank(req.getCount(), req.getLang());
    }

    /**
     * 根据name或seriesName模糊搜索
     * @return 符合条件的游戏视频列表
     */
    public List<GGamesHub> searchByNameOrSeriesName(GamesHubSearchReq req) {
        return mapper.searchByNameOrSeriesName(req.getKeywords(), req.getLang());
    }

    public GamesHubDetailVo getOne(GamesHubDetailReq req) {
        LambdaQueryWrapper<GGamesHub> wrapper = new LambdaQueryWrapper<>(GGamesHub.class);
        wrapper.eq(req.getPath() != null, GGamesHub::getPath, req.getPath());

        GamesHubDetailVo resp = new GamesHubDetailVo();

        GGamesHub one = mapper.selectOne(wrapper);

        if (one == null) {
            return null;
        }

        List<GamesHubDetailVo.Album> album = mapper.selectBySeriesId(one.getSeriesId());
        resp.setId(one.getId());
        resp.setName(one.getName());
        resp.setCover(one.getCover());
        resp.setPath(one.getPath());
        resp.setDuration(one.getDuration());
        resp.setView(one.getView());
        resp.setSeriesId(one.getSeriesId());
        resp.setSeriesName(one.getSeriesName());
        resp.setVideoUrl(one.getVideoUrl());
        resp.setCreateTime(one.getCreateTime());
        resp.setUpdateTime(one.getUpdateTime());

        resp.setAlbumList(album);
        return resp;
    }

}
