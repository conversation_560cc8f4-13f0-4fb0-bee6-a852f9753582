package com.apk.website.service;

import com.alibaba.fastjson.JSON;
import com.apk.website.common.exception.BaseException;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.common.utils.PageUtils;
import com.apk.website.common.vo.PageData;
import com.apk.website.constant.CloudflareVerifyConstant;
import com.apk.website.constant.RemovedReasonConstant;
import com.apk.website.constant.ResponseCodeConstant;
import com.apk.website.constant.StatusConstant;
import com.apk.website.dto.*;
import com.apk.website.dto.admin.ApkDataReq;
import com.apk.website.entity.*;
import com.apk.website.mapper.*;
import com.apk.website.vo.ApkInfoVo;
import com.apk.website.vo.ExtraInfo;
import com.apk.website.vo.PlateDomainRelationVo;
import com.apk.website.vo.resp.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApkDataService {

    private static final String VERIFY_URL = "https://www.google.com/recaptcha/api/siteverify";

    private final ApkDataMapper apkDataMapper;
    private final ApkArticleMapper articleMapper;
    private final ApkDomainRelationMapper apkDomainRelationMapper;
    private final DomainMapper domainMapper;
    private final ArticleDomainRelationMapper articleDomainRelationMapper;
    private final ApkRelationService apkRelationService;

    public PageData list(ApkDataReq req) {
        QueryWrapper<TApkData> queryWrapper = new QueryWrapper<>();
        if (req.getHasPicture() != null && req.getHasPicture() == Boolean.TRUE) {
            queryWrapper.ne("picture", "");
        }
        if (req.getDomainId() != null) {
            List<Long> ids = queryRelationApkIds(req.getDomainId());
            queryWrapper.in("id", ids);
        }
        if (req.getId() != null) {
            queryWrapper.eq("id", req.getId());
        }
        if (StringUtils.isNotBlank(req.getAppName())) {
            queryWrapper.like("app_name", req.getAppName());
        }
        if (StringUtils.isNotBlank(req.getCategory())) {
            queryWrapper.eq("category", req.getCategory());
        }
        if (StringUtils.isNotBlank(req.getSubCategory())) {
            queryWrapper.like("sub_category", req.getSubCategory());
        }
        if (StringUtils.isNotBlank(req.getBundle())) {
            queryWrapper.like("bundle", req.getBundle());
        }
        if (StringUtils.isNotBlank(req.getLanguage())) {
            queryWrapper.like("language", req.getLanguage());
        }
        if (req.getStatus() != null) {
            queryWrapper.eq("status", req.getStatus());
        }
        if (StringUtils.isNotBlank(req.getLanguage())) {
            queryWrapper.eq("language", req.getLanguage());
        }
        if (req.getReason() != null && req.getReason() == 1) { // 0显示全部 1显示正常
            queryWrapper.eq("reason", RemovedReasonConstant.NORMAL.getCode());
        }
        if (StringUtils.isNotBlank(req.getSortField()) && StringUtils.isNotBlank(req.getSortType())) {
            queryWrapper.orderBy(true,req.getSortType().equals("asc"),req.getSortField());
        }
        Page page = new Page<>(req.getPageNum(),req.getPageSize());
        IPage iPage = apkDataMapper.selectPageVoBack(page, queryWrapper);
//        List<ApkInfoVo> list = iPage.getRecords();
//        list.sort((a, b) -> {
//            long installsA = parseInstalls(a.getInstalls());
//            long installsB = parseInstalls(b.getInstalls());
//            return Long.compare(installsB, installsA);
//        });
//        iPage.setRecords(list);
        return PageUtils.coverPage(iPage);
    }

    public int add(ApkDataReq req) {
        TApkData apkData = new TApkData();
        BeanUtils.copyProperties(req, apkData);
        String extraInfo = ExtraInfo.buildExtraInfo(req);
        apkData.setExtraInfo(extraInfo);
        apkData.setDescription("");
        try {
            return apkDataMapper.insert(apkData);
        }catch (DuplicateKeyException e) {
            throw new BaseException(ResponseCodeConstant.DUPLICATE_KEY.getCode(), ResponseCodeConstant.DUPLICATE_KEY.getMsg());
        }
    }

    public int delete(ApkDataReq req) {
        return apkDataMapper.deleteBatchIds(req.getIds());
    }

    @Transactional
    public int update(ApkDataReq req) {
        TApkData apkData = new TApkData();
        BeanUtils.copyProperties(req, apkData);
        String extraInfo = ExtraInfo.buildExtraInfo(req);
        apkData.setExtraInfo(extraInfo);
        int i = 0;
        try {
            i = apkDataMapper.updateById(apkData);
        }catch (DuplicateKeyException e) {
            throw new BaseException(ResponseCodeConstant.DUPLICATE_KEY.getCode(), ResponseCodeConstant.DUPLICATE_KEY.getMsg());
        }


        if(i > 0){
            // 更新relation表
            //删除关联域
            if (req.getWebsites() != null && req.getWebsites().size() > 0) {
                int delete = apkDomainRelationMapper.delete(new LambdaQueryWrapper<TApkDomainRelation>().eq(TApkDomainRelation::getApkId, req.getId()));
                List<TApkDomainRelation> apkDomainRelations=new ArrayList<>();
                for (Integer domainId : req.getWebsites()) {
                    TApkDomainRelation apkDomainRelation = new TApkDomainRelation();
                    apkDomainRelation.setApkId(req.getId());
                    apkDomainRelation.setDomainId(domainId);
                    apkDomainRelation.setStatus(0);
                    apkDomainRelations.add(apkDomainRelation);
                }
                //批量插入
                apkDomainRelationMapper.insertBatch(apkDomainRelations);
            }

            if (req.getWebsites() != null && req.getWebsites().isEmpty()) {
                apkDomainRelationMapper.delete(new LambdaQueryWrapper<TApkDomainRelation>().eq(TApkDomainRelation::getApkId, req.getId()));
            }

        }

        return i;
    }

    public ApkDataResp getOne(ApkDataReq req) {
        CompletableFuture<TApkData> apkDataFuture = CompletableFuture.supplyAsync(() -> {
            TApkData apkData = apkDataMapper.selectById(req.getId());
            return apkData;
        });
        //一个应用会有多个domain
        CompletableFuture<List<Integer>> domainFuture = CompletableFuture.supplyAsync(() -> {
            List<TApkDomainRelation> apkDomainRelations = apkDomainRelationMapper.selectList(new LambdaQueryWrapper<TApkDomainRelation>().eq(TApkDomainRelation::getApkId, req.getId()));
            List<Integer> domainIds = apkDomainRelations.stream().map(TApkDomainRelation::getDomainId).collect(Collectors.toList());
            return domainIds;
        });
        TApkData apkData = null;
        List<Integer> websites= null;
        try {
            apkData = apkDataFuture.join();
            websites = domainFuture.join();
        } catch (Exception e) {
            log.error("getOne接口获取数据异常", e);
            throw new RuntimeException(e);
        }

        ApkDataResp resp = new ApkDataResp();
        BeanUtils.copyProperties(apkData, resp);
        resp.setWebsites(websites);
        if(apkData.getExtraInfo()!=null){
            ExtraInfo extraInfo = JSON.parseObject(apkData.getExtraInfo(), ExtraInfo.class);
            resp.buildExtraInfo(extraInfo);
        }

        return resp;
    }

    public int editStatus(ApkDataReq req) {
        return apkDataMapper.updateStatus(req);
    }

//    ===============================    h5  ==============================================

    /**
     * cloudflare验证
     * @param req
     * @return
     */
    public ApkVerifyResp siteVerify(ApkVerifyReq req) {
        OkHttpClient client = OkHttpClientUtils.CLIENT.getClientInstance();

        String secret = getSecret(req);

        FormBody formBody = new FormBody.Builder()
                .add("secret", secret)
                .add("response", req.getResponse())
                .build();
        Request request = new Request.Builder()
                .url(VERIFY_URL)
                .post(formBody)
                .build();

        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String body = response.body().string();
                ApkVerifyResp resp = JSON.parseObject(body, ApkVerifyResp.class);
                return resp;
            }else {
                log.error("cloudflare限流出错: {}", response.body().string());
                throw new RuntimeException("cloudflare限流出错");
            }
        }catch (Exception e) {
            log.error("cloudflare限流出错: {}", e);
            throw new RuntimeException("cloudflare限流出错");
        }
    }

    private static String getSecret(ApkVerifyReq req) {
        String domain = req.getDomain();
        String secret = CloudflareVerifyConstant.ENTER_GAME.getKey();

        if (CloudflareVerifyConstant.SHOPLUS.getDomain().equals(domain)) {
            secret = CloudflareVerifyConstant.SHOPLUS.getKey();
        }
        if (CloudflareVerifyConstant.APKDEST.getDomain().equals(domain)) {
            secret = CloudflareVerifyConstant.APKDEST.getKey();
        }
        if (CloudflareVerifyConstant.APKSEEKER.getDomain().equals(domain)) {
            secret = CloudflareVerifyConstant.APKSEEKER.getKey();
        }
        return secret;
    }


    public CfCaptchaResponse cfVerify(ApkVerifyReq req) {
        OkHttpClient client = OkHttpClientUtils.CLIENT.getClientInstance();
        String secret = "0x4AAAAAABicWs0DtqDjiSB8FEMFNP_IhaQ";
        String url = "https://challenges.cloudflare.com/turnstile/v0/siteverify";
        FormBody formBody = new FormBody.Builder()
                .add("secret", secret)
                .add("response", req.getResponse())
                .build();
        Request request = new Request.Builder()
                .url(url)
                .post(formBody)
                .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String body = response.body().string();
                CfCaptchaResponse resp = JSON.parseObject(body, CfCaptchaResponse.class);
                return resp;
            }else {
                log.error("cloudflare限流出错: {}", response.body().string());
                throw new RuntimeException("cloudflare限流出错");
            }
        }catch (Exception e) {
            log.error("cloudflare限流出错: {}", e);
            throw new RuntimeException("cloudflare限流出错");
        }
    }

    /**
     * 获取app详情并返回某些app
     * @param req
     * @return
     */
//    public Object detail(ApkDataDetailReq req) {
//        List<Long> ids = queryRelationApkIds(req.getDomain());
//
//        TApkData apkData = apkDataMapper.getByIdOrBundle(req.getId(), req.getLanguage());
//        if (apkData == null) {
//            log.info("查无此app: {}", req.getId());
//            return null;
//        }
//
//        if (StringUtils.isNotBlank(apkData.getOfferUrl())) {
//            apkData.setGoogleUrl(apkData.getOfferUrl());
//        }
//        ApkDetailResp resp = new ApkDetailResp();
//        if (apkData != null) {
//            if (!"".equals(apkData.getOfferUrl())) {
//                resp.setGoogleUrl(apkData.getOfferUrl());
//            }
//            BeanUtils.copyProperties(apkData,resp);
//            resp.setDetailList(spiltDetail(apkData.getDetail()));
//        }
//
//        //  兼容
//        if (StringUtils.isBlank(req.getDomain())) {
//            req.setDomain("apkseeker.com");
//        }
//
//        //查domainId
//        TDomain tDomain = domainMapper.selectOne(new LambdaQueryWrapper<TDomain>().eq(TDomain::getDomain, req.getDomain()));
//        if (tDomain == null){
//            log.info("查无此domain: {}", req.getDomain());
//            return null;
//        }
//        CompletableFuture<List<TApkData>> recommendAppsFuture = CompletableFuture.supplyAsync(() -> {
//            List<Long> apkRelationByApkIdAndType = apkRelationService.getApkIdsByApkIdAndType(apkData.getId(), 0,  tDomain.getId());
//            if  (CollectionUtils.isEmpty(apkRelationByApkIdAndType) ) {
//                //没查到走旧的逻辑
//                return apkDataMapper.getRandAppCategory(ids, apkData.getCategory(), apkData.getSubCategory(),req.getLanguage(), req.getRecommendCounts() == null ? 10 : req.getRecommendCounts());
//            }
//            List<TApkData> apkDataList = apkDataMapper.selectList(new LambdaQueryWrapper<TApkData>().in(TApkData::getId, apkRelationByApkIdAndType));
//            apkDataList.sort(Comparator.comparingInt(data -> apkRelationByApkIdAndType.indexOf(data.getId())));
//            return apkDataList;
//        });
//        CompletableFuture<List<TApkData>> topAppsFuture = CompletableFuture.supplyAsync(() -> {
//            List<Long> apkRelationByApkIdAndType1 = apkRelationService.getApkIdsByApkIdAndType(apkData.getId(), 1,  tDomain.getId());
//            if  (CollectionUtils.isEmpty(apkRelationByApkIdAndType1) ) {
//                //没查到走旧的逻辑
//                return apkDataMapper.getRandApp(ids, req.getLanguage(), req.getRandomCounts() == null ? 10 : req.getRandomCounts());
//            }
//            List<TApkData> apkDataList = apkDataMapper.selectList(new LambdaQueryWrapper<TApkData>().in(TApkData::getId, apkRelationByApkIdAndType1));
//            apkDataList.sort(Comparator.comparingInt(data -> apkRelationByApkIdAndType1.indexOf(data.getId())));
//            return apkDataList;
//        });
////
//        List<TApkData> apkDataList= recommendAppsFuture.join();
//        List<TApkData> randApp = topAppsFuture.join();
//
//        List<ApkDetailResp.RecommendApp> recommendApps = apkDataList.stream()
//                .map(item -> {
//                    ApkDetailResp.RecommendApp recommendApp = new ApkDetailResp.RecommendApp();
//                    BeanUtils.copyProperties(item, recommendApp);
//                    return recommendApp;
//                })
//                .collect(Collectors.toList());
//
//        List<ApkDetailResp.RandomApp> randomApps = randApp.stream()
//                .map(item -> {
//                    ApkDetailResp.RandomApp randomApp = new ApkDetailResp.RandomApp();
//                    BeanUtils.copyProperties(item, randomApp);
//                    Elements elements = Jsoup.parse(item.getDetail()).body().select("p");
//                    for (Element element : elements) {
//                        String desc = element.text();
//                        if (StringUtils.isNotBlank(desc)) {
//                            randomApp.setDesc(desc);
//                            break;
//                        }
//                    }
//                    return randomApp;
//                })
//                .collect(Collectors.toList());
//
//        List<Long> ids1 = recommendApps.stream().map(ApkDetailResp.RecommendApp::getId).collect(Collectors.toList());
//        List<Long> ids2 = randomApps.stream().map(ApkDetailResp.RandomApp::getId).collect(Collectors.toList());
//        ids1.addAll(ids2);
//
//        List<TApkData> list = apkDataMapper.selectList(new LambdaQueryWrapper<>(TApkData.class).notIn(TApkData::getId, ids1).eq(TApkData::getCategory, apkData.getCategory()));
//        Collections.shuffle(list);
//        int effectiveCount = (req.getDevCounts() == null) ? Math.min(60, list.size()) : Math.min(req.getDevCounts(), list.size());
//        List<TApkData> dataList = list.subList(0, effectiveCount);
//
//        List<ApkDetailResp.DevApp> devApps = dataList.stream().map(item -> {
//            ApkDetailResp.DevApp devApp = new ApkDetailResp.DevApp();
//            BeanUtils.copyProperties(item, devApp);
//            Elements elements = Jsoup.parse(item.getDetail()).body().select("p");
//            for (Element element : elements) {
//                String desc = element.text();
//                if (StringUtils.isNotBlank(desc)) {
//                    devApp.setDesc(desc);
//                    break;
//                }
//            }
//            return devApp;
//        }).collect(Collectors.toList());
//
//
//        req.setCategory(apkData.getCategory());
//        req.setSubCategory(apkData.getSubCategory());
//        TApkData otherApk = apkDataMapper.queryByCategory(req);
//        resp.setOtherApkData(otherApk);
//
//        resp.setRecommendApps(recommendApps);
//        resp.setRandomApps(randomApps);
//        resp.setDevApps(devApps);
//
//        return resp;
//    }

    // todo
    public ApkDetailResp devDetail(ApkDataDetailReq req) {
        return null;
    }

    public ApkDetailResp detail(ApkDataDetailReq req) {
        List<Long> ids = queryRelationApkIds(req.getDomain());

        TApkData apkData = apkDataMapper.getByIdOrBundle(req.getId(), req.getLanguage());
        if (apkData == null) {
            log.info("查无此app: {}", req.getId());
            return null;
        }

        if (StringUtils.isNotBlank(apkData.getOfferUrl())) {
            apkData.setGoogleUrl(apkData.getOfferUrl());
        }
        ApkDetailResp resp = new ApkDetailResp();
        if (apkData != null) {
            if (!"".equals(apkData.getOfferUrl())) {
                resp.setGoogleUrl(apkData.getOfferUrl());
            }
            BeanUtils.copyProperties(apkData,resp);
            resp.setDetailList(spiltDetail(apkData.getDetail()));
        }

        //  兼容
        if (StringUtils.isBlank(req.getDomain())) {
            req.setDomain("apkseeker.com");
        }

        //查domainId
        TDomain tDomain = domainMapper.selectOne(new LambdaQueryWrapper<TDomain>().eq(TDomain::getDomain, req.getDomain()));
        if (tDomain == null){
            log.info("查无此domain: {}", req.getDomain());
            return null;
        }



        Integer recommendCounts = req.getRecommendCounts() == null ? 10: req.getRecommendCounts();
        Integer randomCounts = req.getRandomCounts() == null ? 10: req.getRandomCounts();
        Integer devCounts = req.getDevCounts() == null ? 60: req.getDevCounts();
        Integer totalCounts = recommendCounts + randomCounts + devCounts;

        List<TApkData> allApps = apkDataMapper.getRandAppCategory(ids, apkData.getCategory(), apkData.getSubCategory(), req.getLanguage(), totalCounts);


        int from = 0;

        // 1. recommend 段
        List<TApkData> recommendList = allApps.subList(from, from + recommendCounts);
        from += recommendCounts;

        // 2. random 段
        List<TApkData> randomList = allApps.subList(from, from + randomCounts);
        from += randomCounts;

        // 3. dev 段
        List<TApkData> devList = allApps.subList(from, from + devCounts);


        List<ApkDetailResp.RecommendApp> recommendApps = recommendList.stream()
                .map(item -> {
                    ApkDetailResp.RecommendApp recommendApp = new ApkDetailResp.RecommendApp();
                    BeanUtils.copyProperties(item, recommendApp);
                    return recommendApp;
                })
                .collect(Collectors.toList());

        List<ApkDetailResp.RandomApp> randomApps = randomList.stream()
                .map(item -> {
                    ApkDetailResp.RandomApp randomApp = new ApkDetailResp.RandomApp();
                    BeanUtils.copyProperties(item, randomApp);
                    Elements elements = Jsoup.parse(item.getDetail()).body().select("p");
                    for (Element element : elements) {
                        String desc = element.text();
                        if (StringUtils.isNotBlank(desc)) {
                            randomApp.setDesc(desc);
                            break;
                        }
                    }
                    return randomApp;
                })
                .collect(Collectors.toList());



        List<ApkDetailResp.DevApp> devApps = devList.stream().map(item -> {
            ApkDetailResp.DevApp devApp = new ApkDetailResp.DevApp();
            BeanUtils.copyProperties(item, devApp);
            Elements elements = Jsoup.parse(item.getDetail()).body().select("p");
            for (Element element : elements) {
                String desc = element.text();
                if (StringUtils.isNotBlank(desc)) {
                    devApp.setDesc(desc);
                    break;
                }
            }
            return devApp;
        }).collect(Collectors.toList());

        List<TApkData> hotGames = apkDataMapper.getHotGamesRandom(ids, req.getLanguage(),12);
        List<TApkData> sameSubCategoryHotGames = apkDataMapper.sameSubCategoryHotGames(ids, apkData.getCategory(), apkData.getSubCategory(), req.getLanguage(), 10);

        req.setCategory(apkData.getCategory());
        req.setSubCategory(apkData.getSubCategory());
        TApkData otherApk = apkDataMapper.queryByCategory(req);
        resp.setOtherApkData(otherApk);

        resp.setRecommendApps(recommendApps);
        resp.setRandomApps(randomApps);
        resp.setDevApps(devApps);
        resp.setHotGames(hotGames);
        resp.setSameSubCategoryHotGames(sameSubCategoryHotGames);

        List<Integer> articleIds = queryRelationArticleIds(tDomain.getId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(articleIds)) {
            List<TApkArticle> articles = articleMapper.selectList(new LambdaQueryWrapper<>(TApkArticle.class).in(TApkArticle::getId, articleIds));
            Collections.shuffle(articles);
            List<TApkArticle> subList = articles.subList(0, Math.min(6, articles.size()));

            resp.setArticles(subList);
        }


        return resp;
    }



    private static List<ApkDetailResp.DetailTag> spiltDetail(String detail) {
        Document doc = Jsoup.parse(detail);
        Elements elements = doc.select("h1, h2, h3, h4, p, ul, blockquote");
        List<ApkDetailResp.DetailTag> tags = new ArrayList<>();

        for (int i = 0; i < elements.size(); i++) {
            Element current = elements.get(i);
            String html = current.outerHtml();
            String tagName = current.tagName();

            ApkDetailResp.DetailTag tag = new ApkDetailResp.DetailTag();
            tag.setTag(tagName);
            tag.setText(html);

            tags.add(tag);

        }

        return tags;
    }

    public ApkSearchResp search(ApkSearchReq req) {
        List<TApkData> list = apkDataMapper.getByNameOrBundle(req);
        List<Long> ids = queryRelationApkIds(req.getDomain());
        List<TApkData> filteredList = list.stream()
                .filter(apkData -> ids.contains(apkData.getId()))
                .collect(Collectors.toList());

        filteredList.sort((a, b) -> {
            long installsA = parseInstalls(a.getInstalls());
            long installsB = parseInstalls(b.getInstalls());
            return Long.compare(installsB, installsA);
        });
        Integer count = req.getCount();
        if (count != null && count < list.size()) {
            filteredList = filteredList.subList(0, Math.min(filteredList.size(), count));
        }
        List<ApkSearchResp.SearchApp> target = filteredList.stream().map(item -> {
            ApkSearchResp.SearchApp app = new ApkSearchResp.SearchApp();
            BeanUtils.copyProperties(item, app);
            String desc = "";
            try {
                desc = Jsoup.parse(item.getDetail()).body().select("p").first().text();

            }catch (NullPointerException e) {

            }
            app.setDesc(desc);
            return app;
        }).collect(Collectors.toList());
        // 随机给几个
        // todo 关联表查询
        List<TApkData> data = apkDataMapper.getRandApp(ids, req.getLanguage(), 10);
        List<ApkSearchResp.SearchApp> random = data.stream()
                .map(item -> {
                    ApkSearchResp.SearchApp app = new ApkSearchResp.SearchApp();
                    String desc = "";
                    try {
                        desc = Jsoup.parse(item.getDetail()).body().select("p").first().text();
                    }catch (NullPointerException e) {

                    }
                    app.setDesc(desc);
                    BeanUtils.copyProperties(item, app);
                    return app;
                }).collect(Collectors.toList());

        ApkSearchResp resp = new ApkSearchResp();
        resp.setResult(target);
        resp.setOther(random);

        return resp;
    }

    // 解析installs字段，将M、K、B转换为实际数值
    private long parseInstalls(String installs) {
        if (installs == null || installs.isEmpty()) {
            return 0;
        }
        installs = installs.trim().toUpperCase();
        if (installs.endsWith("M+")) {
            return Long.parseLong(installs.replace("M+", "")) * 1000000;
        } else if (installs.endsWith("K+")) {
            return Long.parseLong(installs.replace("K+", "")) * 1000;
        } else if (installs.endsWith("B+")) {
            return Long.parseLong(installs.replace("B+", "")) * 1000000000;
        } else if (installs.matches("\\d+")) { // 纯数字
            return Long.parseLong(installs);
        }
        return 0;
    }

    /**
     * 查出关联的app
     * @param domain
     * @return
     */
    private List<Long> queryRelationApkIds(String domain) {
        TDomain tDomain = domainMapper.selectOne(new LambdaQueryWrapper<>(TDomain.class).eq(TDomain::getDomain, domain));
        if (tDomain == null) {
            return Collections.emptyList();
        }
        Integer domainId = tDomain.getId();
        LambdaQueryWrapper<TApkDomainRelation> wrapper = new LambdaQueryWrapper<>(TApkDomainRelation.class);
        wrapper.eq(TApkDomainRelation::getDomainId, domainId);
        List<TApkDomainRelation> apkDomainRelations = apkDomainRelationMapper.selectList(wrapper);
        return apkDomainRelations.stream().map(TApkDomainRelation::getApkId).collect(Collectors.toList());
    }

    private List<Integer> queryRelationArticleIds(Integer domainId) {
        LambdaQueryWrapper<TArticleDomainRelation> wrapper = new LambdaQueryWrapper<>(TArticleDomainRelation.class);
        wrapper.eq(TArticleDomainRelation::getDomainId, domainId);
        List<TArticleDomainRelation> apkDomainRelations = articleDomainRelationMapper.selectList(wrapper);
        return apkDomainRelations.stream().map(TArticleDomainRelation::getArticleId).collect(Collectors.toList());
    }

    private List<Long> queryRelationApkIds(Integer domainId) {
        LambdaQueryWrapper<TApkDomainRelation> wrapper = new LambdaQueryWrapper<>(TApkDomainRelation.class);
        wrapper.eq(TApkDomainRelation::getDomainId, domainId);
        List<TApkDomainRelation> apkDomainRelations = apkDomainRelationMapper.selectList(wrapper);
        return apkDomainRelations.stream().map(TApkDomainRelation::getApkId).collect(Collectors.toList());
    }

    public Map<String, String> getIconByCategory(ApkDataDetailReq req) {
        List<Long> ids = queryRelationApkIds(req.getDomain());
        LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
        wrapper.in(TApkData::getId, ids);
        wrapper.eq(TApkData::getCategory, req.getCategory());
        wrapper.eq(TApkData::getStatus, StatusConstant.OPEN.getCode());
        List<TApkData> apkDataList = apkDataMapper.selectList(wrapper);
        Map<String, TApkData> resultMap = apkDataList.stream()
                .collect(Collectors.toMap(
                        TApkData::getSubCategory,
                        apkData -> apkData,
                        (existing, replacement) -> existing));


        Map<String, String> result = new HashMap<>();
        resultMap.forEach((k,v) -> {
            result.put(k, v.getImg());
        });

        return result;
    }

    public Map<String, List<ApkHomePageResp>> getByCategories(HomePageReq req) {
        Map<String,List<ApkHomePageResp>> map = new HashMap<>();
        if (req.getLanguage() == null || "".equals(req.getLanguage())) {
            req.setLanguage("en");
        }
        // todo 加缓存
        List<Long> ids = queryRelationApkIds(req.getDomain());
        for (String category : req.getCategory()) {
            LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
            wrapper.eq(TApkData::getLanguage, req.getLanguage());
            wrapper.eq(TApkData::getSubCategory, category);
            wrapper.eq(TApkData::getStatus, StatusConstant.OPEN.getCode());
            wrapper.in(TApkData::getId, ids);
            IPage page = new Page(1, req.getCounts());
            List<TApkData> data = apkDataMapper.selectList(page, wrapper);
            List<ApkHomePageResp> result = data.stream().map(item -> {
                ApkHomePageResp apkHomePageResp = new ApkHomePageResp();
                try {
                    BeanUtils.copyProperties(item, apkHomePageResp);
                    String desc = Jsoup.parse(item.getDetail()).body().select("p").first().text();
                    apkHomePageResp.setDesc(desc);
                } catch (Exception e) {
                    log.error("exception: {}", e);
                }
                return apkHomePageResp;
            }).collect(Collectors.toList());
            map.put(category, result);
        }

        return map;
    }

    public List<String> getCategories(TopAppsReq req) {
        List<Long> ids = queryRelationApkIds(req.getDomain());
        LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
        wrapper.in(TApkData::getId, ids);
        if (StringUtils.isNotBlank(req.getCategory())) {
            wrapper.eq(TApkData::getCategory, req.getCategory());
        }
        if (StringUtils.isNotBlank(req.getSubCategory())) {
            wrapper.eq(TApkData::getSubCategory, req.getSubCategory());
        }
        wrapper.eq(TApkData::getLanguage, req.getLanguage());
        List<TApkData> list = apkDataMapper.selectList(wrapper);
        return list.stream().map(TApkData::getSubCategory).distinct().collect(Collectors.toList());
    }

    public PageData<ApkHomePageResp> page(TopAppsReq req) {
        List<Long> ids = queryRelationApkIds(req.getDomain());
        LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
        if (!CollectionUtils.isEmpty(ids)) {
            wrapper.in(TApkData::getId, ids);
        }
        wrapper.eq(TApkData::getStatus, StatusConstant.OPEN.getCode());
        if (StringUtils.isNotBlank(req.getCategory())) {
            wrapper.eq(TApkData::getCategory, req.getCategory());
        }
        if (StringUtils.isNotBlank(req.getSubCategory())) {
            wrapper.eq(TApkData::getSubCategory, req.getSubCategory());
        }
        wrapper.eq(TApkData::getLanguage, req.getLanguage());
        IPage page = new Page(req.getPageNum(), req.getPageSize());
        IPage iPage = apkDataMapper.selectPageVo(page, wrapper);
        return PageUtils.coverPage(iPage);
    }

    public List<PlateDomainRelationVo> homepage(HomePageReq req) {
        return null;
    }

    public List<ApkInfoVo> getTopDownload(TopAppsReq req) {
        List<Long> ids = queryRelationApkIds(req.getDomain());
        LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
        wrapper.eq(TApkData::getLanguage, req.getLanguage());
        wrapper.in(TApkData::getId, ids);
        wrapper.eq(TApkData::getStatus, StatusConstant.OPEN.getCode());
        if (StringUtils.isNotBlank(req.getCategory())) {
            wrapper.eq(TApkData::getCategory, req.getCategory());
        }
        if (StringUtils.isNotBlank(req.getSubCategory())) {
            wrapper.eq(TApkData::getSubCategory, req.getSubCategory());
        }
        IPage page = new Page(req.getPageNum(), req.getPageSize());
        List<TApkData> list = apkDataMapper.selectList(page, wrapper);
        return list.stream().map(tApkData -> {
            ApkInfoVo vo = new ApkInfoVo();
            BeanUtils.copyProperties(tApkData, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 搜索页默认数据
     * @param req
     * @return
     */
    public ApkSearchIndexResp searchPage(ApkSearchReq req) {
        List<Long> ids = queryRelationApkIds(req.getDomain());
        LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
        wrapper.eq(TApkData::getLanguage, req.getLanguage());
        wrapper.in(TApkData::getId, ids);
        wrapper.eq(TApkData::getStatus, StatusConstant.OPEN.getCode());
        List<TApkData> list = apkDataMapper.selectList(wrapper);

        Map<String, List<TApkData>> groupedByCategory = list.stream()
                .collect(Collectors.groupingBy(TApkData::getCategory));

        List<TApkData> apps = new ArrayList<>();
        List<TApkData> games = new ArrayList<>();
        if (req.getLanguage().equals("en")) {
            apps = groupedByCategory.get("Apps");
            games = groupedByCategory.get("Games");
        }else {
            apps = groupedByCategory.get("アプリ");
            games = groupedByCategory.get("ゲーム");
        }


        int seed = LocalDate.now().getDayOfYear();
        Random random = new Random(seed);

        Collections.shuffle(apps, random);
        List<ApkSearchIndexResp.App> topApps = apps.stream().limit(req.getCounts()).map(item -> {
            ApkSearchIndexResp.App app = new ApkSearchIndexResp.App();
            BeanUtils.copyProperties(item, app);
            return app;
        }).collect(Collectors.toList());

        Collections.shuffle(games, random);
        List<ApkSearchIndexResp.App> topGames = games.stream().limit(req.getCounts()).map(item -> {
            ApkSearchIndexResp.App app = new ApkSearchIndexResp.App();
            BeanUtils.copyProperties(item, app);
            return app;
        }).collect(Collectors.toList());

        List<String> hotApps = apps.stream().limit(5).map(TApkData::getAppName).collect(Collectors.toList());
        List<String> hotGames = games.stream().limit(5).map(TApkData::getAppName).collect(Collectors.toList());

        hotApps.addAll(hotGames);

        ApkSearchIndexResp resp = new ApkSearchIndexResp();
        resp.setTopApps(topApps);
        resp.setTopGames(topGames);
        resp.setHotWords(hotApps);

        return resp;

    }

    public List<String> getSubCategory(String category) {

        LambdaQueryWrapper<TApkData> distinctSubCategory = new QueryWrapper<TApkData>().select("distinct sub_category").lambda().eq(StringUtils.isNotBlank(category),TApkData::getCategory, category);

        List<TApkData> tApkData = apkDataMapper.selectList(distinctSubCategory);
        if (CollectionUtils.isEmpty(tApkData)){
            return Collections.emptyList();
        }
        return tApkData.stream().map(TApkData::getSubCategory).collect(Collectors.toList());
    }

//    public PageData<ApkHomePageResp> pages(TopAppsReq req) {
//        List<Long> ids = queryRelationApkIds(req.getDomain());
//
//        LambdaQueryWrapper<TApkData> wrapper2 = new LambdaQueryWrapper<>(TApkData.class);
//        wrapper2.in(TApkData::getId, ids);
//        wrapper2.eq(TApkData::getStatus, StatusConstant.OPEN.getCode());
//        wrapper2.eq(TApkData::getLanguage,req.getLanguage());
//        if (StringUtils.isNotBlank(req.getCategory())) {
//            wrapper2.eq(TApkData::getCategory, req.getCategory());
//        }
//        Long total = apkDataMapper.selectCount(wrapper2);
//
//        LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
//        wrapper.in(TApkData::getId, ids);
//        wrapper.eq(TApkData::getStatus, StatusConstant.OPEN.getCode());
//        if (StringUtils.isNotBlank(req.getCategory())) {
//            wrapper.eq(TApkData::getCategory, req.getCategory());
//        }
//        if (StringUtils.isNotBlank(req.getSubCategory())) {
//            wrapper.eq(TApkData::getSubCategory, req.getSubCategory());
//        }
//        wrapper.eq(TApkData::getLanguage, req.getLanguage());
//        IPage page = new Page(req.getPageNum(), req.getPageSize());
//        IPage iPage = apkDataMapper.selectPageVo(page, wrapper);
//
//
//
//        if (req.getPageNum() > iPage.getPages() || iPage.getRecords().size() < req.getPageSize()) {
//            LambdaQueryWrapper<TApkData> wrapper1 = new LambdaQueryWrapper<>(TApkData.class);
//            wrapper1.in(TApkData::getId, ids);
//            wrapper1.eq(TApkData::getStatus, StatusConstant.OPEN.getCode());
//            wrapper1.eq(TApkData::getLanguage, req.getLanguage());
//            if (StringUtils.isNotBlank(req.getCategory())) {
//                wrapper1.eq(TApkData::getCategory, req.getCategory());
//            }
//            if (StringUtils.isNotBlank(req.getSubCategory())) {
//                wrapper1.notIn(TApkData::getSubCategory, req.getSubCategory());
//            }
//            IPage page1 = new Page(req.getPageNum()- iPage.getPages() , req.getPageSize());
//            IPage iPage1 = apkDataMapper.selectPageVo2(page1, wrapper1);
//            iPage1.setTotal(total);
//            return PageUtils.coverPage(iPage1);
//        }
//
//        iPage.setTotal(total);
//        return PageUtils.coverPage(iPage);
//    }

    public PageData<ApkHomePageResp> pages(TopAppsReq req) {
        List<Long> ids = queryRelationApkIds(req.getDomain());
        LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
        wrapper.in(TApkData::getId, ids);
        wrapper.eq(TApkData::getStatus, StatusConstant.OPEN.getCode());
        if (StringUtils.isNotBlank(req.getCategory())) {
            wrapper.eq(TApkData::getCategory, req.getCategory());
        }
        wrapper.eq(TApkData::getLanguage, req.getLanguage());
        log.info("wrapper: {}", req.getIsRank());
        if (req.getIsRank()) {
            wrapper.eq(TApkData::getSource, "rank");
        }
        IPage page = new Page(req.getPageNum(), req.getPageSize());
        IPage<ApkHomePageResp> iPage;
        if (req.getIsRank()) {
            iPage = apkDataMapper.selectPageVo3(page, wrapper);
        }else {
            iPage = apkDataMapper.selectPageVo2(page, wrapper);
        }
        return PageUtils.coverPage(iPage);
    }


}
