package com.apk.website.service;

import com.apk.website.common.exception.BaseException;
import com.apk.website.common.utils.PageUtils;
import com.apk.website.common.vo.PageData;
import com.apk.website.constant.ResponseCodeConstant;
import com.apk.website.dto.admin.DomainReq;
import com.apk.website.entity.TApkArticle;
import com.apk.website.entity.TDomain;
import com.apk.website.mapper.DomainMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DomainService {

    private final DomainMapper domainMapper;

    public PageData list(DomainReq req) {
        LambdaQueryWrapper<TDomain> queryWrapper = new LambdaQueryWrapper<>(TDomain.class);
        if (req.getId() != null) {
            queryWrapper.eq(TDomain::getId, req.getId());
        }
        if (req.getDomain() != null) {
            queryWrapper.like(TDomain::getDomain, req.getDomain());
        }
        if (req.getName() != null) {
            queryWrapper.like(TDomain::getName, req.getName());
        }
        if (req.getStatus() != null) {
            queryWrapper.eq(TDomain::getStatus, req.getStatus());
        }
        Page page = new Page<>(req.getPageNum(),req.getPageSize());
        IPage iPage = domainMapper.selectPage(page, queryWrapper);
        return PageUtils.coverPage(iPage);
    }

    public int add(DomainReq req) {
        TDomain tDomain = new TDomain();
        BeanUtils.copyProperties(req, tDomain);
        try {
            return domainMapper.insert(tDomain);
        }catch (DuplicateKeyException e) {
            throw new BaseException(ResponseCodeConstant.DUPLICATE_KEY.getCode(), ResponseCodeConstant.DUPLICATE_KEY.getMsg());
        }
    }

    public int delete(DomainReq req) {
        return domainMapper.deleteBatchIds(req.getIds());
    }

    public int update(DomainReq req) {
        TDomain tDomain = new TDomain();
        BeanUtils.copyProperties(req, tDomain);
        try {
            return domainMapper.updateById(tDomain);
        }catch (DuplicateKeyException e) {
            throw new BaseException(ResponseCodeConstant.DUPLICATE_KEY.getCode(), ResponseCodeConstant.DUPLICATE_KEY.getMsg());
        }

    }


    public TDomain getOne(DomainReq req) {
        return domainMapper.selectById(req.getId());
    }

    public int updateStatus(DomainReq req) {
        return domainMapper.updateStatus(req.getId(), req.getStatus());
    }
}
