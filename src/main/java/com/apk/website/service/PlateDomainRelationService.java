package com.apk.website.service;

import com.alibaba.fastjson.JSON;
import com.apk.website.constant.StatusConstant;
import com.apk.website.constant.TypeConstant;
import com.apk.website.dto.admin.PlateDomainDataDto;
import com.apk.website.dto.admin.PlateDomainRelationReq;
import com.apk.website.entity.*;
import com.apk.website.mapper.*;
import com.apk.website.vo.PlateAppInfoVo;
import com.apk.website.vo.PlateArticleInfoVo;
import com.apk.website.vo.PlateDomainRelationVo;
import com.apk.website.vo.resp.ApkArticleJsonResp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/7
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlateDomainRelationService {

    private final PlateDomainRelationMapper plateDomainRelationMapper;
    private final ApkDataMapper apkDataMapper;
    private final ApkArticleMapper articleMapper;
    private final ApkDomainRelationMapper apkDomainRelationMapper;
    private final ArticleDomainRelationMapper articleDomainRelationMapper;
    private final DomainMapper domainMapper;

    public List<PlateDomainRelationVo> list(PlateDomainRelationReq req) {
        if (req.getDomainStatus() != null && req.getDomainStatus() == 1) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<TPlateDomainRelation> queryWrapper = new LambdaQueryWrapper<>(TPlateDomainRelation.class);

        if (req.getDomainId() != null) {
            queryWrapper.eq(TPlateDomainRelation::getDomainId, req.getDomainId());
        }
        if (StringUtils.isNotBlank(req.getPageType())) {
            queryWrapper.eq(TPlateDomainRelation::getPageType, req.getPageType());
        }
        List<TPlateDomainRelation> list = plateDomainRelationMapper.selectList(queryWrapper);

        if (list.isEmpty()) {
            return null;
        }

        List<PlateDomainRelationVo> voList = getPlateDomainRelationVos(list);

        return voList;
    }

    private List<PlateDomainRelationVo> getPlateDomainRelationVos(List<TPlateDomainRelation> list) {
        List<PlateDomainRelationVo> voList = new ArrayList<>();
        for (TPlateDomainRelation plateDomainRelation : list) {
            PlateDomainRelationVo relationVo = new PlateDomainRelationVo();
            BeanUtils.copyProperties(plateDomainRelation, relationVo);
            String data = plateDomainRelation.getData();
            List<PlateDomainDataDto> appInfos = JSON.parseArray(data, PlateDomainDataDto.class);
            List<Integer> ids = appInfos.stream().map(PlateDomainDataDto::getId).collect(Collectors.toList());
            if (plateDomainRelation.getDataType() == TypeConstant.APK.getCode()) {
                LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
                wrapper.in(TApkData::getId, ids);
                wrapper.eq(TApkData::getStatus, StatusConstant.OPEN.getCode());
                List<TApkData> apkDataList = apkDataMapper.selectList(wrapper);
                Map<Long, TApkData> apkDataMap = apkDataList.stream()
                        .collect(Collectors.toMap(TApkData::getId, Function.identity()));
                Map<Integer, String> bannerInfo = appInfos.stream()
                        .collect(Collectors.toMap(PlateDomainDataDto::getId, PlateDomainDataDto::getBanner));


                List<Object> appInfoList = new ArrayList<>();
                for (Integer id : ids) {
                    TApkData apkData = apkDataMap.get(Long.valueOf(id));
                    if (apkData != null) {
                        PlateAppInfoVo vo = new PlateAppInfoVo();
                        BeanUtils.copyProperties(apkData, vo);

                        String picture = bannerInfo.get(id);
                        if (StringUtils.isNotBlank(picture)) {
                            vo.setBanner(picture);
                        }
//                        if (plateDomainRelation.getPlateType().equals("icon_overflow_card")) {
                        Elements paragraphs = Jsoup.parse(apkData.getDetail()).body().select("p");
                        String firstNonEmptyParagraph = "";
                        for (Element paragraph : paragraphs) {
                            String text = paragraph.text().trim();
                            if (!text.isEmpty()) {
                                firstNonEmptyParagraph = text;
                                break;
                            }
                        }
                        vo.setDesc(firstNonEmptyParagraph);
//                        }
                        appInfoList.add(vo);
                    }
                }

                relationVo.setData(appInfoList);
                voList.add(relationVo);
            } else {
                List<Object> articleInfoVos = new ArrayList<>();

                LambdaQueryWrapper<TApkArticle> wrapper = new LambdaQueryWrapper<>(TApkArticle.class);
                wrapper.in(TApkArticle::getId, ids);
                List<TApkArticle> articleList = articleMapper.selectList(wrapper);
                Map<Integer, TApkArticle> articleMap = articleList.stream()
                        .collect(Collectors.toMap(TApkArticle::getId, Function.identity()));
                for (Integer id : ids) {
                    TApkArticle apkArticle = articleMap.get(id);
                    if (apkArticle != null) {
                        PlateArticleInfoVo vo = new PlateArticleInfoVo();
                        BeanUtils.copyProperties(apkArticle, vo);
                        List<ApkArticleJsonResp.Content> contents = JSON.parseArray(apkArticle.getContent(), ApkArticleJsonResp.Content.class);
                        if (CollectionUtils.isNotEmpty(contents)) {
                            List<ApkArticleJsonResp.Content> collect = contents.stream().filter(content -> "p".equals(content.getTag())).collect(Collectors.toList());
                            vo.setDesc(collect.get(0).getContent());
                        }
                        articleInfoVos.add(vo);
                    }
                }

                relationVo.setData(articleInfoVos);
                voList.add(relationVo);
            }
        }
        return voList;
    }

    @Transactional(rollbackFor = Exception.class)
    public int add(List<PlateDomainRelationReq> req) {
        // 删掉旧的
        PlateDomainRelationReq deleteItem = req.get(0);
        Integer domainId = deleteItem.getDomainId();
        String pageType = deleteItem.getPageType();
        LambdaQueryWrapper<TPlateDomainRelation> deleteWrapper = new LambdaQueryWrapper<>(TPlateDomainRelation.class);
        deleteWrapper.eq(TPlateDomainRelation::getDomainId, domainId);
        deleteWrapper.eq(TPlateDomainRelation::getPageType, pageType);
        plateDomainRelationMapper.delete(deleteWrapper);

        int sort = 1;
        int count = 0;

        for (PlateDomainRelationReq item : req) {
            TPlateDomainRelation dao = new TPlateDomainRelation();
            BeanUtils.copyProperties(item, dao);
            dao.setSort(sort);
            sort++;
            count += plateDomainRelationMapper.insert(dao);
            saveRelation(item, domainId);

        }

        return count;
    }

    // 保存关联关系
    private void saveRelation(PlateDomainRelationReq item, Integer domainId) {
        if (item.getDataType() == TypeConstant.APK.getCode()) {
            String data = item.getData();
            List<PlateDomainDataDto> appInfos = JSON.parseArray(data, PlateDomainDataDto.class);
            for (PlateDomainDataDto appInfo : appInfos) {
                Integer apkId = appInfo.getId();
                TApkDomainRelation apkRelation = new TApkDomainRelation();
                apkRelation.setDomainId(domainId);
                apkRelation.setApkId(Long.valueOf(apkId));
                try {
                    apkDomainRelationMapper.insert(apkRelation);
                } catch (Exception e) {
                    log.error("保存文章关联关系失败: domainID: {}, apkId{}, exception: {}", data, apkId, e);
                }
            }
        } else {
            String data = item.getData();
            List<PlateDomainDataDto> articleInfos = JSON.parseArray(data, PlateDomainDataDto.class);
            for (PlateDomainDataDto articleInfo : articleInfos) {
                Integer articleId = articleInfo.getId();
                TArticleDomainRelation articleDomainRelation = new TArticleDomainRelation();
                articleDomainRelation.setDomainId(domainId);
                articleDomainRelation.setArticleId(articleId);
                try {
                    articleDomainRelationMapper.insert(articleDomainRelation);
                } catch (Exception e) {
                    log.error("保存文章关联关系失败: domainID: {}, apkId{}, exception: {}", data, articleId, e);
                }
            }
        }
    }

    public int delete(PlateDomainRelationReq req) {
        return plateDomainRelationMapper.deleteBatchIds(req.getIds());
    }

    public int edit(PlateDomainRelationReq req) {
        TPlateDomainRelation dao = new TPlateDomainRelation();
        BeanUtils.copyProperties(req, dao);
        return plateDomainRelationMapper.updateById(dao);
    }

    //    ===========================  站点接口  ==============================
    public List<PlateDomainRelationVo> homepage(PlateDomainRelationReq req) {

        // 查询domain_id
        LambdaQueryWrapper<TDomain> domainWrapper = new LambdaQueryWrapper<>(TDomain.class);
        domainWrapper.eq(TDomain::getDomain, req.getDomain());
        TDomain domain = domainMapper.selectOne(domainWrapper);
        Integer domainId = domain.getId();

        LambdaQueryWrapper<TPlateDomainRelation> queryWrapper = new LambdaQueryWrapper<>(TPlateDomainRelation.class);
        queryWrapper.eq(TPlateDomainRelation::getDomainId, domainId);
        if (StringUtils.isNotBlank(req.getPageType())) {
            queryWrapper.eq(TPlateDomainRelation::getPageType, req.getPageType());
        }
        List<TPlateDomainRelation> list = plateDomainRelationMapper.selectList(queryWrapper);

        if (list.isEmpty()) {
            return null;
        }

        List<PlateDomainRelationVo> voList = getPlateDomainRelationVos(list);
        return voList;
    }
}
