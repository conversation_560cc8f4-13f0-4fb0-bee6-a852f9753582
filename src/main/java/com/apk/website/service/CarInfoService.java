package com.apk.website.service;

import com.apk.website.common.utils.PageUtils;
import com.apk.website.dto.CarInfoReq;
import com.apk.website.entity.TCarInfo;
import com.apk.website.vo.BaseResponse;
import com.apk.website.vo.resp.CarInfoResp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.apk.website.mapper.TCarInfoMapper;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【t_car_info】的数据库操作Service实现
* @createDate 2025-06-05 09:37:38
*/
@Service
public class CarInfoService extends ServiceImpl<TCarInfoMapper, TCarInfo>
    implements IService<TCarInfo> {

    @Autowired
    private TCarInfoMapper carInfoMapper;
    public BaseResponse listCarInfo(CarInfoReq req) {
        LambdaQueryWrapper<TCarInfo> tCarInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tCarInfoLambdaQueryWrapper.eq(StringUtils.hasText(req.getSubCategory()), TCarInfo::getSubCategory, req.getSubCategory())
                .eq(StringUtils.hasText(req.getState()),TCarInfo::getState, req.getState())
                .eq(StringUtils.hasText(req.getCity()),TCarInfo::getCity, req.getCity())
                .eq(StringUtils.hasText(req.getBrand()),TCarInfo::getBrand, req.getBrand())
                .eq(StringUtils.hasText(req.getType()),TCarInfo::getType, req.getType())
                .between(StringUtils.hasText(req.getMinPrice())&& StringUtils.hasText(req.getMaxPrice()),TCarInfo::getPrice,  req.getMinPrice(), req.getMaxPrice());
        if (StringUtils.hasText(req.getOrderBy())){
            String orderBy = req.getOrderBy();
            if ("low".equals(orderBy)){
                tCarInfoLambdaQueryWrapper.orderByAsc(TCarInfo::getPrice);
            }else {
                tCarInfoLambdaQueryWrapper.orderByDesc(TCarInfo::getPrice);
            }
        }
        Page<TCarInfo> tCarInfoPage = carInfoMapper.selectPage(Page.of(req.getPageNum(), req.getPageSize()), tCarInfoLambdaQueryWrapper);
        List<TCarInfo> collect = tCarInfoPage.getRecords().stream().map(carInfo -> {
            String description = carInfo.getDescription();
            Document parse = Jsoup.parse(description);
            String text = parse.getAllElements().text();
            carInfo.setDescription(text);
            return carInfo;
        }).collect(Collectors.toList());
        tCarInfoPage.setRecords(collect);
        return BaseResponse.success(PageUtils.coverPage(tCarInfoPage));
    }

    public BaseResponse<?> getOneCarInfo(CarInfoReq req) {
        if (req.getId() == null){
            return BaseResponse.fail(400, "id不能为空");
        }
        TCarInfo tCarInfo = carInfoMapper.selectById(req.getId());
        List<TCarInfo> types =carInfoMapper.selectTypes(req.getType(),tCarInfo.getState(),10);
        List<TCarInfo> brands =carInfoMapper.selectBrands(req.getBrand(),tCarInfo.getState(),6);

        List<TCarInfo> list = carInfoMapper.selectRandom();
        List<TCarInfo> list10 = list.subList(0, 10);
        List<TCarInfo> list6 = list.subList(10,16).stream().map(carInfo -> {
            String description = carInfo.getDescription();
            Document parse = Jsoup.parse(description);
            String text = parse.getAllElements().text();
            carInfo.setDescription(text);
            return carInfo;
        }).collect(Collectors.toList());



        CarInfoResp carInfoResp = new CarInfoResp();
        BeanUtils.copyProperties(tCarInfo, carInfoResp);
        carInfoResp.setList10(list10);
        carInfoResp.setList6(list6);
        carInfoResp.setTypes(types);
        carInfoResp.setBrands(brands);
        return BaseResponse.success(carInfoResp);
    }
}




