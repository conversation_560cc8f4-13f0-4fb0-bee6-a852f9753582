package com.apk.website.service;

import com.apk.website.common.exception.BaseException;
import com.apk.website.dto.admin.ArticleDomainRelationReq;
import com.apk.website.entity.TApkArticle;
import com.apk.website.entity.TArticleDomainRelation;
import com.apk.website.mapper.ApkArticleMapper;
import com.apk.website.mapper.ArticleDomainRelationMapper;
import com.apk.website.vo.ArticleInfoVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleDomainRelationService {

    private final ArticleDomainRelationMapper articleDomainRelationMapper;

    private final ApkArticleMapper articleMapper;

    public List<ArticleInfoVo> list(ArticleDomainRelationReq req) {
        if (req.getDomainId() == null) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"请选择域名！");
        }
        LambdaQueryWrapper<TArticleDomainRelation> queryWrapper = new LambdaQueryWrapper<>(TArticleDomainRelation.class);
        queryWrapper.eq(TArticleDomainRelation::getDomainId, req.getDomainId());
        if (req.getStatus() != null) {
            queryWrapper.eq(TArticleDomainRelation::getStatus, req.getStatus());
        }
        IPage page = new Page(req.getPageNum(), req.getPageSize());
        IPage iPage = articleDomainRelationMapper.selectPage(page, queryWrapper);
        List<TArticleDomainRelation> list = iPage.getRecords();

        if (list == null || list.isEmpty()) {
            return null;
        }

        List<Integer> apkIds = list.stream().map(TArticleDomainRelation::getArticleId).collect(Collectors.toList());
        LambdaQueryWrapper<TApkArticle> articleQueryWrapper = new LambdaQueryWrapper<>(TApkArticle.class);
        articleQueryWrapper.in(TApkArticle::getId, apkIds);
        List<TApkArticle> articles = articleMapper.selectList(articleQueryWrapper);

        return articles.stream().map(tApkArticle -> {
            ArticleInfoVo infoVo = new ArticleInfoVo();
            BeanUtils.copyProperties(tApkArticle, infoVo);
            return infoVo;
        }).collect(Collectors.toList());

    }

    public int batchAdd(ArticleDomainRelationReq req) {
        List<Integer> articleIds = req.getArticleIds();
        if (articleIds == null || articleIds.isEmpty()) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"请选择apk！");
        }
        if (req.getDomainId() == null) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"请选择域名！");
        }
        int count = 0;
        for (Integer articleId : articleIds) {
            TArticleDomainRelation relation = new TArticleDomainRelation();
            relation.setArticleId(articleId);
            relation.setDomainId(req.getDomainId());
            relation.setStatus(0);
            count += articleDomainRelationMapper.insert(relation);
        }

        return count;
    }

    public int batchDel(ArticleDomainRelationReq req) {
        return articleDomainRelationMapper.deleteBatchIds(req.getIds());
    }

    public List<ArticleInfoVo> unbindArticles(ArticleDomainRelationReq req) {
        if (req.getDomainId() == null) {
            throw new BaseException(HttpStatus.BAD_REQUEST.value(),"请选择域名！");
        }
        // 已经绑定的
        LambdaQueryWrapper<TArticleDomainRelation> queryWrapper = new LambdaQueryWrapper<>(TArticleDomainRelation.class);
        queryWrapper.eq(TArticleDomainRelation::getDomainId, req.getDomainId());
        List<TArticleDomainRelation> bindArticles = articleDomainRelationMapper.selectList(queryWrapper);
        List<Integer> articleIds = bindArticles.stream().map(TArticleDomainRelation::getArticleId).collect(Collectors.toList());

        IPage page = new Page(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<TApkArticle> wrapper = new LambdaQueryWrapper<>(TApkArticle.class);
        if (articleIds != null && articleIds.size() > 0) {
            wrapper.notIn(TApkArticle::getId, articleIds);
        }
        List<TApkArticle> articles = articleMapper.selectList(page, wrapper);
        return articles.stream().map(tApkArticle -> {
            ArticleInfoVo infoVo = new ArticleInfoVo();
            BeanUtils.copyProperties(tApkArticle, infoVo);
            return infoVo;
        }).collect(Collectors.toList());
    }
}
