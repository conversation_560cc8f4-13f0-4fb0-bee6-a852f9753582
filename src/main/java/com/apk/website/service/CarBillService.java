package com.apk.website.service;

import com.apk.website.entity.TCarBill;
import com.apk.website.vo.BaseResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apk.website.mapper.TCarBillMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
* <AUTHOR>
* @description 针对表【t_car_bill】的数据库操作Service实现
* @createDate 2025-06-05 09:36:59
*/
@Service
public class CarBillService extends ServiceImpl<TCarBillMapper, TCarBill>
    implements IService<TCarBill> {

    public BaseResponse listBill(String state) {
        LambdaQueryWrapper<TCarBill> tCarBillLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tCarBillLambdaQueryWrapper.eq(TCarBill::getState, state);
        return BaseResponse.success(this.baseMapper.selectOne(tCarBillLambdaQueryWrapper));
    }

}




