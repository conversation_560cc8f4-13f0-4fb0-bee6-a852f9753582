package com.apk.website.service;

import com.apk.website.common.utils.PageUtils;
import com.apk.website.dto.LocationReq;
import com.apk.website.dto.MajorCityReq;
import com.apk.website.entity.TLocation;
import com.apk.website.entity.TMajorCityLocation;
import com.apk.website.vo.BaseResponse;
import com.apk.website.vo.resp.LocationInfoResp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.apk.website.mapper.TLocationMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【t_location(地理位置表)】的数据库操作Service实现
* @createDate 2025-06-05 09:33:25
*/
@Service
public class LocationService extends ServiceImpl<TLocationMapper, TLocation>
    implements IService<TLocation> {


    public BaseResponse list(LocationReq req) {

        LambdaQueryWrapper<TLocation> tLocationLambdaQueryWrapper = new LambdaQueryWrapper<>();
        tLocationLambdaQueryWrapper
                .eq(StringUtils.hasText(req.getCategory()),TLocation::getCategory, req.getCategory())
                .eq(StringUtils.hasText(req.getSubCategory()),TLocation::getSubCategory, req.getSubCategory())
                .eq(StringUtils.hasText(req.getState()),TLocation::getState, req.getState())
                .eq(StringUtils.hasText(req.getCity()),TLocation::getCity, req.getCity())
                .eq(StringUtils.hasText(req.getBrand()),TLocation::getBrand, req.getBrand())
                .eq(StringUtils.hasText(req.getCommonService()),TLocation::getCommonService, req.getCommonService());
        //分页
        Page<TLocation> tLocationPage = baseMapper.selectPage(Page.of(req.getPageNum(), req.getPageSize()), tLocationLambdaQueryWrapper);
        return BaseResponse.success(PageUtils.coverPage(tLocationPage));

//        return BaseResponse.success(this.list(tLocationLambdaQueryWrapper));
    }


    public BaseResponse<LocationInfoResp> getOneLocation(Long id) {
        TLocation location = baseMapper.selectById(id);
        List<TLocation> tLocations = baseMapper.selectNearBy( 4);
        LocationInfoResp locationInfoResp = new LocationInfoResp();
        locationInfoResp.setNearby(tLocations);
        BeanUtils.copyProperties(location, locationInfoResp);
        return BaseResponse.success(locationInfoResp);
    }
}




