package com.apk.website.service;

import com.apk.website.common.utils.PageUtils;
import com.apk.website.common.vo.PageData;
import com.apk.website.dto.MajorCityReq;
import com.apk.website.entity.TMajorCityLocation;
import com.apk.website.vo.BaseResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.apk.website.mapper.TMajorCityLocationMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
* <AUTHOR>
* @description 针对表【t_major_city_location】的数据库操作Service实现
* @createDate 2025-06-05 09:37:31
*/
@Service
public class MajorCityLocationService extends ServiceImpl<TMajorCityLocationMapper, TMajorCityLocation>
    implements IService<TMajorCityLocation> {
    @Autowired
    private TMajorCityLocationMapper majorCityLocationMapper;

    public BaseResponse listMajorCity(MajorCityReq req) {
        LambdaQueryWrapper<TMajorCityLocation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.hasText(req.getMajorCity()),TMajorCityLocation::getMajorCity, req.getMajorCity());
        Page<TMajorCityLocation> tMajorCityLocationPage = majorCityLocationMapper.selectPage(Page.of(req.getPageNum(), req.getPageSize()), wrapper);
        PageData<TMajorCityLocation> tMajorCityLocationPageData = PageUtils.coverPage(tMajorCityLocationPage);
        return BaseResponse.success(tMajorCityLocationPageData);
    }

    public BaseResponse getOneMajorCity(Long id) {
        return BaseResponse.success(majorCityLocationMapper.selectById(id));
    }
}




