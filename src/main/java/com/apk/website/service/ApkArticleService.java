package com.apk.website.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.apk.website.common.utils.PageUtils;
import com.apk.website.common.vo.PageData;
import com.apk.website.dto.ArticleReq;
import com.apk.website.dto.admin.ApkArticleReq;
import com.apk.website.dto.admin.DomainReq;
import com.apk.website.entity.*;
import com.apk.website.mapper.*;
import com.apk.website.vo.resp.ApkArticleJsonResp;
import com.apk.website.vo.resp.ApkArticleResp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qiniu.storage.ApiUploadV1MakeBlock;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/4/27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApkArticleService {

    private final ApkArticleMapper mapper;
    private final ArticleDomainRelationMapper articleDomainRelationMapper;
    private final DomainMapper domainMapper;
    private final ApkArticleMapper articleMapper;
    private final ApkDataMapper apkDataMapper;
    private final ApkDomainRelationMapper apkDomainRelationMapper;


    public PageData list(ApkArticleReq req) {
        LambdaQueryWrapper<TApkArticle> queryWrapper = new LambdaQueryWrapper<>(TApkArticle.class);
        if (req.getId() != null) {
            queryWrapper.eq(TApkArticle::getId, req.getId());
        }
        if (req.getTitle() != null) {
            queryWrapper.like(TApkArticle::getTitle, req.getTitle());
        }
        if (req.getStatus() != null) {
            queryWrapper.eq(TApkArticle::getStatus, req.getStatus());
        }
        if (req.getCategory() != null) {
            queryWrapper.eq(TApkArticle::getCategory, req.getCategory());
        }
        if (req.getSubCategory() != null) {
            queryWrapper.eq(TApkArticle::getSubCategory, req.getSubCategory());
        }
        Page page = new Page<>(req.getPageNum(),req.getPageSize());
        IPage iPage = mapper.selectPage(page, queryWrapper);
        return PageUtils.coverPage(iPage);
    }

    public int add(ApkArticleReq req) {
        TApkArticle apkArticle = new TApkArticle();
        BeanUtils.copyProperties(req, apkArticle);
        return mapper.insert(apkArticle);
    }


    public int update(ApkArticleReq req) {
        TApkArticle apkArticle = new TApkArticle();
        BeanUtils.copyProperties(req, apkArticle);
        return mapper.updateById(apkArticle);
    }

    public int delete(ApkArticleReq req) {
        return mapper.deleteBatchIds(req.getIds());
    }

    public TApkArticle getOne(DomainReq req) {
        return mapper.selectById(req.getId());
    }

    public PageData<TApkArticle> articleList(ApkArticleReq req) {
        List<Integer> ids = queryRelationArticleIds(req.getDomain());
        LambdaQueryWrapper<TApkArticle> wrapper = new LambdaQueryWrapper<>(TApkArticle.class);
        wrapper.in(TApkArticle::getId, ids);
        IPage page = new Page(req.getPageNum(),req.getPageSize());
        IPage<TApkArticle> iPage = articleMapper.selectPage(page, wrapper);
        for (TApkArticle record : iPage.getRecords()) {
            List<ApkArticleJsonResp.Content> contents = JSON.parseArray(record.getContent(), ApkArticleJsonResp.Content.class);
            List<ApkArticleJsonResp.Content> collect = contents.stream().filter(content -> "p".equals(content.getTag())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                record.setContent(collect.get(0).getContent());
            }
        }
        return PageUtils.coverPage(iPage);
    }


    /**
     * 查出关联的文章
     * @param domain
     * @return
     */
    private List<Integer> queryRelationArticleIds(String domain) {
        TDomain tDomain = domainMapper.selectOne(new LambdaQueryWrapper<>(TDomain.class).eq(TDomain::getDomain, domain));
        Integer domainId = tDomain.getId();
        LambdaQueryWrapper<TArticleDomainRelation> wrapper = new LambdaQueryWrapper<>(TArticleDomainRelation.class);
        wrapper.eq(TArticleDomainRelation::getDomainId, domainId);
        List<TArticleDomainRelation> apkDomainRelations = articleDomainRelationMapper.selectList(wrapper);
        return apkDomainRelations.stream().map(TArticleDomainRelation::getArticleId).collect(Collectors.toList());
    }

    public ApkArticleJsonResp articleDetailByJson(ArticleReq req) {
        TApkArticle article = articleMapper.selectById(req.getId());
        ApkArticleJsonResp resp = new ApkArticleJsonResp();
        resp.setId(article.getId());
        resp.setTitle(article.getTitle());
        resp.setCategory(article.getCategory());
        resp.setSubCategory(article.getSubCategory());
        resp.setImg(article.getImg());
        resp.setCreateTime(article.getCreateTime());
        List<ApkArticleJsonResp.Content> content = JSON.parseObject(article.getContent(), new TypeReference<List<ApkArticleJsonResp.Content>>() {});
        resp.setContent(content);

        LambdaQueryWrapper<TApkArticle> wrapper = new LambdaQueryWrapper<>(TApkArticle.class);
        List<Integer> ids = queryRelationArticleIds(req.getDomain());
        List<Integer> filterIds = ids.stream().filter(id -> id != req.getId()).collect(Collectors.toList());
        wrapper.in(TApkArticle::getId, filterIds);
        List<TApkArticle> apkArticles = articleMapper.selectList(wrapper);

        List<ApkArticleJsonResp.Article> moreArticles = apkArticles.stream().map(item -> {
            ApkArticleJsonResp.Article articleItem = new ApkArticleJsonResp.Article();
            List<ApkArticleJsonResp.Content> contents = JSON.parseArray(item.getContent(), ApkArticleJsonResp.Content.class);
            List<ApkArticleJsonResp.Content> collect = contents.stream().filter(content1 -> "p".equals(content1.getTag())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                articleItem.setDesc(collect.get(0).getContent());
            }
            BeanUtils.copyProperties(item, articleItem);
            return articleItem;
        }).collect(Collectors.toList());
        resp.setMoreArticles(moreArticles);

        List<Long> apkIds = queryRelationApkIds(req.getDomain());
        List<TApkData> apps = apkDataMapper.selectByInstallsRank(apkIds,null, null, req.getAppCounts(), "en");
        List<ApkArticleJsonResp.App> moreApps = apps.stream().map(item -> {
            ApkArticleJsonResp.App app = new ApkArticleJsonResp.App();
            BeanUtils.copyProperties(item, app);
            return app;
        }).collect(Collectors.toList());
        resp.setMoreApps(moreApps);

        return resp;
    }

    private List<Long> queryRelationApkIds(String domain) {
        TDomain tDomain = domainMapper.selectOne(new LambdaQueryWrapper<>(TDomain.class).eq(TDomain::getDomain, domain));
        Integer domainId = tDomain.getId();
        LambdaQueryWrapper<TApkDomainRelation> wrapper = new LambdaQueryWrapper<>(TApkDomainRelation.class);
        wrapper.eq(TApkDomainRelation::getDomainId, domainId);
        List<TApkDomainRelation> apkDomainRelations = apkDomainRelationMapper.selectList(wrapper);
        return apkDomainRelations.stream().map(TApkDomainRelation::getApkId).collect(Collectors.toList());
    }
}
