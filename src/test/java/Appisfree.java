import com.alibaba.fastjson.JSON;
import com.apk.website.Application;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.entity.TApkArticle;
import com.apk.website.entity.TApkData;
import com.apk.website.entity.TApkDomainRelation;
import com.apk.website.entity.TArticleDomainRelation;
import com.apk.website.mapper.ApkArticleMapper;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.mapper.ApkDomainRelationMapper;
import com.apk.website.mapper.ArticleDomainRelationMapper;
import com.apk.website.schedule.utils.QiniuUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.qiniu.util.Json;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@ActiveProfiles("gcp_us_west1")
@Slf4j
public class Appisfree {

    @Autowired
    ApkArticleMapper mapper;

    @Autowired
    ApkDataMapper apkDataMapper;

    @Autowired
    QiniuUtils qiniuUtils;


    // 历史版本
    @Test
    public void main1() throws Exception {
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();

        String url = "https://apkpure.com/cn/google-play-store/com.android.vending/versions";
        Request request = OkHttpClientUtils.buildRequest(url);
        Response resp = httpClient.newCall(request).execute();
        String qiniuToken = qiniuUtils.getQiniuToken();
        if (resp.isSuccessful()) {
            List<AppVersionInfo> versions = new ArrayList<>();

            Document parse = Jsoup.parse(resp.body().string());
            Elements elements = parse.select("a.ver_download_link");
            List<Element> elements1 = elements.subList(0, Math.min(5, elements.size()));
            int i = 0;
            for (Element element : elements1) {
                i++;
                String version = element.select("div.ver-item-n").first().text();
                String size = element.select("span.ver-item-s").first().text();
                String date = element.select("span.update-on").first().text();

                String href = element.attr("href");
                Request req = OkHttpClientUtils.buildRequest(href);
                Response resp1 = httpClient.newCall(req).execute();
                if(resp1.isSuccessful()) {
                    Document parse1 = Jsoup.parse(resp1.body().string());
                    String vUrl = parse1.select("div.download-box").first().select("a").first().attr("href");
                    String myFileName = qiniuUtils.downloadAndUpload(vUrl, "com.android.vending-" + i, qiniuToken, 3);

                    AppVersionInfo versionInfo = new AppVersionInfo();
                    versionInfo.setVersion(version);
                    versionInfo.setSize(size);
                    versionInfo.setDate(date);
                    versionInfo.setUrl(myFileName);
                    versions.add(versionInfo);
                }


            }
            String versionJson = JSON.toJSONString(versions);
            System.out.println();
        }
    }


    @Test
    public void uploadApkList() throws Exception {

//        List<String> bundles = allBundles();
        List<String> bundles = Arrays.asList("com.openai.chatgpt","com.facebook.orca","com.roblox.client");

        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        String reqUrl = "https://www.appisfree.com/version/%s";
        for (String bundle : bundles) {
            String qiniuToken = qiniuUtils.getQiniuToken();

            String versionUrl = String.format(reqUrl, bundle);
            Request request = OkHttpClientUtils.buildRequest(versionUrl);
            Response resp = httpClient.newCall(request).execute();
            if (resp.isSuccessful()) {
                List<AppVersionInfo> versions = new ArrayList<>();
                Document parse = Jsoup.parse(resp.body().string());
                Elements elements = null;
                try {
                    elements = parse.select("div.category").first().select("ul").first().select("li");
                }catch (Exception e) {
                    log.info("{} 无历史版本", bundle);
                    continue;
                }
                List<Element> elements1 = elements.subList(0, Math.min(5, elements.size()));
                for (Element element : elements1) {
                    Element info = element.select(".itemInfo").first();
                    String version = info.select(".textShow").first().text();
                    String size = info.select(".itemDes").first().select(".size").text();
                    String date = info.select(".itemDes").first().select(".date").text();
                    String url = element.select(".itemLink").attr("href");
                    String fileName = url.substring(url.lastIndexOf('/') + 1);
//                    String myFileName = qiniuUtils.downloadAndUpload(url, fileName, qiniuToken, 3);
                    AppVersionInfo versionInfo = new AppVersionInfo();
                    versionInfo.setVersion(version);
                    versionInfo.setSize(size);
                    versionInfo.setDate(date);
                    versionInfo.setUrl(fileName);
                    versions.add(versionInfo);
                }
                String versionJson = JSON.toJSONString(versions);

                log.info("已保存: {}", bundle);

            }else {
                log.warn("{} 无历史版本", bundle );
            }
        }

    }


//    public void hello1() throws Exception {
////        List<String> bundles = allBundles();
//        List<String> bundles = Arrays.asList("com.roblox.client");
//        bundles.remove("com.openai.chatgpt");
//
//        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
//        String reqUrl = "https://www.appisfree.com/version/%s";
//
//        // 创建一个线程池，指定核心线程数为 20，最大线程数为 60，空闲线程存活时间为 1 分钟
//        int corePoolSize = 20;  // 核心线程数
//        int maximumPoolSize = 60;  // 最大线程数
//        long keepAliveTime = 1L;  // 空闲线程的存活时间（单位：分钟）
//        TimeUnit timeUnit = TimeUnit.MINUTES;  // 存活时间的单位
//
//        // 队列，用于存放待执行的任务
//        BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(1000);
//
//        // 创建线程池
//        ExecutorService executor = new ThreadPoolExecutor(
//                corePoolSize,
//                maximumPoolSize,
//                keepAliveTime,
//                timeUnit,
//                workQueue,
//                new ThreadPoolExecutor.AbortPolicy()  // 饱和策略：直接在调用者线程中执行任务
//        );
//
//        for (String bundle : bundles) {
//            try {
//                String qiniuToken = qiniuUtils.getQiniuToken();
//                String versionUrl = String.format(reqUrl, bundle);
//                Request request = OkHttpClientUtils.buildRequest(versionUrl);
//                Response resp = httpClient.newCall(request).execute();
//
//                if (resp.isSuccessful()) {
//                    List<AppVersionInfo> versions = new ArrayList<>();
//                    Document parse = Jsoup.parse(resp.body().string());
//                    Elements elements = parse.select("div.category").first().select("ul").first().select("li");
//
//                    // 在这里使用线程池来并行处理每个元素
//                    CountDownLatch elementLatch = new CountDownLatch(elements.size());
//
//                    for (Element element : elements) {
//                        executor.submit(() -> {
//                            try {
//                                Element info = element.select(".itemInfo").first();
//                                String version = info.select(".textShow").first().text();
//                                String size = info.select(".itemDes").first().select(".size").text();
//                                String date = info.select(".itemDes").first().select(".date").text();
//                                String url = element.select(".itemLink").attr("href");
//                                String fileName = url.substring(url.lastIndexOf('/') + 1);
//                                String myFileName = qiniuUtils.downloadAndUpload(url, fileName, qiniuToken, 3);
//
//                                AppVersionInfo versionInfo = new AppVersionInfo();
//                                versionInfo.setVersion(version);
//                                versionInfo.setSize(size);
//                                versionInfo.setDate(date);
//                                versionInfo.setUrl(myFileName);
//                                synchronized (versions) {
//                                    versions.add(versionInfo);
//                                }
//                            } catch (Exception e) {
//                                log.error("处理 element 时出错", e);
//                            } finally {
//                                elementLatch.countDown();
//                            }
//                        });
//                    }
//
//                    // 等待所有元素的线程执行完成
//                    elementLatch.await();
//
//                    String versionJson = JSON.toJSONString(versions);
//                    apkDataMapper.updateVersion(bundle, versionJson);
//                    log.info("已保存: {}", bundle);
//                } else {
//                    log.warn("{} 无历史版本", bundle);
//                }
//            } catch (Exception e) {
//                log.error("处理 {} 时出错", bundle, e);
//            } finally {
//            }
//        }
//
//        // 关闭线程池
//        executor.shutdown();
//    }

    @Autowired
    ApkDomainRelationMapper relationMapper;

    @Autowired
    ArticleDomainRelationMapper articleDomainRelationMapper;

    @Test
    public void bind_article() {
        Integer domainId = 39;
        for (int i = 283; i <= 324; i++) {
            TArticleDomainRelation relation = new TArticleDomainRelation();
            relation.setArticleId(i);
            relation.setDomainId(domainId);
            relation.setCreateTime(new Date());
            relation.setUpdateTime(new Date());
            try {
                articleDomainRelationMapper.insert(relation);
            }catch (DuplicateKeyException e) {
                log.error("已存在");
            }
        }
    }

    @Test
    public void bind_apk() throws IOException {
        Integer domainId = 39;
        // hot页面
//        List<String> bundles = allBundles();

        // 首页
//        String url = "https://www.appisfree.com/";
//        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
//        Request request = OkHttpClientUtils.buildRequest(url);
//        Response resp = httpClient.newCall(request).execute();
//        List<String> bundles = new ArrayList<>();
//        if (resp.isSuccessful()) {
//            Document document = Jsoup.parse(resp.body().string());
//            Elements categoryEles = document.select("div.categoryList").first().select("div.category");
//            for (Element categoryEle : categoryEles) {
//
//                Elements apps = categoryEle.select("ul.itemList").first().select("li.item");
//                for (Element app : apps) {
//                    String href = app.select("a.itemLink").attr("href");
//                    href = href.replace("/detail/", "");
//                    bundles.add(href);
//                }
//            }
//        }

        // topCharts页面
        String url = "https://www.appisfree.com/topCharts-app";
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        Request request = OkHttpClientUtils.buildRequest(url);
        Response resp = httpClient.newCall(request).execute();
        List<String> bundles = new ArrayList<>();

        if (resp.isSuccessful()) {
            Document document = Jsoup.parse(resp.body().string());
            Element appList = document.select("div.category").first().select("ul.itemList").first();
            Elements apps = appList.select("li.item");
            for (Element app : apps) {
                String href = app.select("a.itemLink").attr("href");
                href = href.replace("/detail/", "");
                bundles.add(href);
            }
        }

        List<TApkData> list = apkDataMapper.selectList(new LambdaQueryWrapper<>(TApkData.class).in(TApkData::getBundle, bundles).eq(TApkData::getLanguage, "en"));
        for (TApkData apkData : list) {
            Long apkDataId = apkData.getId();
            TApkDomainRelation relation = new TApkDomainRelation();
            relation.setApkId(apkDataId);
            relation.setDomainId(domainId);
            relation.setStatus(0);
            try {
                relationMapper.insert(relation);
            } catch (DuplicateKeyException e) {
                log.error("{} 已经绑定", apkDataId);
            }

        }
    }

    private static List<String> allBundles() throws IOException {
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        List<String> bundles = new ArrayList<>();
//        String url = "https://www.appisfree.com/hotApi?application=apps&page=%s";

        String url = "https://www.appisfree.com/hotApi?page=%s&application=undefined";

        for (int i = 1; i <= 4; i++) {
            String reqUrl = String.format(url, i);
            Request request = OkHttpClientUtils.buildRequest(reqUrl);
            Response resp = httpClient.newCall(request).execute();
            if (resp.isSuccessful()) {
                AppResponse appResponse = JSON.parseObject(resp.body().string(), AppResponse.class);
                List<String> bundle = appResponse.getHotAppList().stream().map(AppResponse.App::getAndroidPackageName).collect(Collectors.toList());
                bundles.addAll(bundle);
            }
        }
        url = "https://www.appisfree.com/hotApi?application=games&page=%s";
        for (int i = 1; i <= 4; i++) {
            String reqUrl = String.format(url, i);
            Request request = OkHttpClientUtils.buildRequest(reqUrl);
            Response resp = httpClient.newCall(request).execute();
            if (resp.isSuccessful()) {
                AppResponse appResponse = JSON.parseObject(resp.body().string(), AppResponse.class);
                List<String> bundle = appResponse.getHotAppList().stream().map(AppResponse.App::getAndroidPackageName).collect(Collectors.toList());
                bundles.addAll(bundle);
            }
        }
        return bundles;
    }

    // 热门应用页面
    @Test
    public void test_hotApps() throws IOException {
        List<String> appBundles = allBundles();
        List<TApkData> list = apkDataMapper.selectList(new LambdaQueryWrapper<>(TApkData.class).in(TApkData::getBundle, appBundles).eq(TApkData::getLanguage, "en"));

        String jsonString = JSON.toJSONString(list);
        System.out.println();
    }


    @Test
    public void test_apkVersion() throws Exception {
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        String versionUrl = "https://www.appisfree.com/version/com.instagram.android";
        Request request = OkHttpClientUtils.buildRequest(versionUrl);
        Response resp = httpClient.newCall(request).execute();
        String qiniuToken = qiniuUtils.getQiniuToken();
        if (resp.isSuccessful()) {
            List<AppVersionInfo> versions = new ArrayList<>();
            Document parse = Jsoup.parse(resp.body().string());
            Elements elements = parse.select("div.category").first().select("ul").first().select("li");
            for (Element element : elements) {
                Element info = element.select(".itemInfo").first();
                String version = info.select(".textShow").first().text();
                String size = info.select(".itemDes").first().select(".size").text();
                String date = info.select(".itemDes").first().select(".date").text();
                String url = elements.select(".itemLink").attr("href");
                String fileName = url.substring(url.lastIndexOf('/') + 1);
                String myFileName = qiniuUtils.downloadAndUpload(url, fileName, qiniuToken, 3);
                AppVersionInfo versionInfo = new AppVersionInfo();
                versionInfo.setVersion(version);
                versionInfo.setSize(size);
                versionInfo.setDate(date);
                versionInfo.setUrl(myFileName);
                versions.add(versionInfo);
                System.out.println();
            }
            String versionJson = JSON.toJSONString(versions);

            System.out.println();
        }
    }

    @Test
    public void test() throws IOException {
        String url = "https://www.appisfree.com/hotApi?application=games&page=%s";
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        List<String> queryBundles = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            String reqUrl = String.format(url, i);
            Request request = OkHttpClientUtils.buildRequest(reqUrl);
            Response resp = httpClient.newCall(request).execute();
            if (resp.isSuccessful()) {
                AppResponse appResponse = JSON.parseObject(resp.body().string(), AppResponse.class);
                List<String> bundles = appResponse.getHotAppList().stream().map(AppResponse.App::getAndroidPackageName).collect(Collectors.toList());
                List<TApkData> list = apkDataMapper.selectList(new LambdaQueryWrapper<>(TApkData.class).in(TApkData::getBundle, bundles));

                List<String> missingBundles = bundles.stream()
                        .filter(b -> list.stream().noneMatch(apk -> b.equals(apk.getBundle())))
                        .collect(Collectors.toList());
                queryBundles.addAll(missingBundles);
                System.out.println();
            }
            System.out.println();
        }
        String jsonString = JSON.toJSONString(queryBundles);
        System.out.println();
    }


    // 文章
    @Test
    public void test_article() throws Exception {
        String url = "https://www.appisfree.com/editorsChoiceApi?page=%s";
        String cdnUrl = "https://cdn.appisfree.com/";

        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        String qiniuToken = qiniuUtils.getQiniuToken();

        List<String> notFund = new ArrayList<>();

        for (int i = 1; i <= 2; i++) {
            String reqUrl = String.format(url, i);
            Request request = OkHttpClientUtils.buildRequest(reqUrl);
            Response resp = httpClient.newCall(request).execute();
            if (resp.isSuccessful()) {
                TopicListResponse topicListResponse = JSON.parseObject(resp.body().string(), TopicListResponse.class);
                List<TopicListResponse.Topic> topicList = topicListResponse.getTopicList();
                for (TopicListResponse.Topic topic : topicList) {

                    String imgUrl = qiniuUtils.downloadAndUpload(cdnUrl + topic.getLogo(), UUID.randomUUID().toString(), qiniuToken, 3);
                    List<Cot> cots = parseHtml(topic.getDetailInfo(), notFund);
                    String content = JSON.toJSONString(cots);

                    TApkArticle article = new TApkArticle();
                    article.setTitle(topic.getTitle());
                    article.setImg(imgUrl);

                    article.setContent(content);
                    mapper.insert(article);
                    System.out.println();
                }
            }
        }
    }

    private List<Cot> parseHtml(String text, List<String> notFund) {
        Document parse = Jsoup.parse(text);
        Element body = parse.body();
        for (Element element : body.getAllElements()) {
            if (element.text().equals("{AD}")) {
                element.remove();
            }
        }
        List<Cot> cots = new ArrayList<>();
        for (Element element : parse.body().children()) {
            if (element.text().contains("{app=")) {
                String bundle = element.text().replaceAll(".*app=([a-zA-Z0-9\\.]+).*", "$1");
                TApkData tApkData = apkDataMapper.selectOne(new LambdaQueryWrapper<>(TApkData.class).eq(TApkData::getBundle, bundle).eq(TApkData::getLanguage, "en"));
                if (tApkData == null) {
                    log.info("{} 为空", bundle);
                    notFund.add(bundle);
                    continue;
                }
                AppInfo appInfo = new AppInfo();
                appInfo.setAppName(tApkData.getAppName());
                appInfo.setImg(tApkData.getImg());
                appInfo.setCategory(tApkData.getCategory());
                appInfo.setSubCategory(tApkData.getSubCategory());
                appInfo.setScore(tApkData.getScore());
                appInfo.setInstalls(tApkData.getInstalls());
                appInfo.setBanner(JSON.parseArray(tApkData.getBanners(), String.class).get(0));

                String content = JSON.toJSONString(appInfo);
                String type = "app";
                String tag = "app";
                Cot cot = new Cot(content, tag, type);
                cots.add(cot);

            } else {
                String content = "";
                if (element.tagName().equals("p")) {
                    content = element.childNode(0).toString();
                } else {
                    content = element.text();
                }
                String tag = element.tagName();
                String type = "html";
                Cot cot = new Cot(content, tag, type);
                cots.add(cot);
            }
        }
        return cots;
    }

    @Data
    static class AppVersionInfo {
        private String img;
        private String version;
        private String size;
        private String date;
        private String url;
    }

    @Data
    static class AppInfo {
        private String appName;
        private String img;
        private String bundle;
        private String category;
        private String installs;
        private float score;
        private String subCategory;
        private String banner;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class Cot {
        private String content;
        private String tag;
        private String type;
    }

    @Data
    static class TopicListResponse {

        private List<Topic> topicList;
        private int pageCount;

        @Data
        public static class Topic {
            private int id;
            private String title;
            private String logo;
            private String banner;
            private String keywords;
            private String desc;
            private String route;
            private String detailInfo;
            private String updatedAt;
            private String updatedTime;
        }
    }

    @Data
    static class AppResponse {

        private List<App> hotAppList;
        private int pageCount;

        @Data
        public static class App {
            private String category;
            private String AndroidPackageName;
        }
    }

    @Test
    public void test_homepage() throws IOException {
        String url = "https://www.appisfree.com/";
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        Request request = OkHttpClientUtils.buildRequest(url);
        Response resp = httpClient.newCall(request).execute();
        Map<String, List<String>> map = new HashMap<>();
        if (resp.isSuccessful()) {
            Document document = Jsoup.parse(resp.body().string());
            Elements categoryEles = document.select("div.categoryList").first().select("div.category");
            for (Element categoryEle : categoryEles) {
                String title = categoryEle.select("h1.title").text();
                if (title == null || "".equals(title)) {
                    title = categoryEle.select("h2.title").text();
                }
                List<String> bundles = new ArrayList<>();
                Elements apps = categoryEle.select("ul.itemList").first().select("li.item");
                for (Element app : apps) {
                    String href = app.select("a.itemLink").attr("href");
                    href = href.replace("/detail/", "");

                    // todo 查库比较，如果没有，记录bundle，去Import测试类导入
//                    TApkData tApkData =
//                            apkDataMapper.selectOne(new LambdaQueryWrapper<>(TApkData.class).eq(TApkData::getLanguage, "en").eq(TApkData::getBundle, "bundle"));
//
//                    if (tApkData == null) {
//                        log.error("{} 不存在", href);
//                    }

                    bundles.add(href);
                }
                map.put(title,bundles);
            }
            map.remove("Editor's Choice");
        }


        map.remove("Editor's Choice");
        List<HomePageApp> result = new ArrayList<>();
        List<String> strings = map.get("Hot Games");
        strings.add("com.easygames.race");

        map.forEach((k,v) -> {
            HomePageApp app = new HomePageApp();
            app.setTitle(k);
            List<TApkData> list = apkDataMapper.selectList(new LambdaQueryWrapper<>(TApkData.class).in(TApkData::getBundle, v).eq(TApkData::getLanguage, "en"));
            app.setList(list);
            result.add(app);
        });

        String jsonString = JSON.toJSONString(result);
        System.out.println();
    }

    @Test
    public void test_topCharts() throws IOException {
        String url = "https://www.appisfree.com/topCharts-game";
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        Request request = OkHttpClientUtils.buildRequest(url);
        Response resp = httpClient.newCall(request).execute();
        List<String> bundles = new ArrayList<>();

        if (resp.isSuccessful()) {
            Document document = Jsoup.parse(resp.body().string());
            Element appList = document.select("div.category").first().select("ul.itemList").first();
            Elements apps = appList.select("li.item");
            for (Element app : apps) {
                String href = app.select("a.itemLink").attr("href");
                href = href.replace("/detail/", "");
                bundles.add(href);
            }
        }
        List<TApkData> list = apkDataMapper.selectList(new LambdaQueryWrapper<>(TApkData.class).in(TApkData::getBundle, bundles).eq(TApkData::getLanguage, "en"));
        String jsonString = JSON.toJSONString(list);
        System.out.println();
    }



    @Data
    static class HomePageApp {
        private String title;
        private List<TApkData> list;
    }

}
