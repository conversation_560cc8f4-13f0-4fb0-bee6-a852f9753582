import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import com.apk.website.Application;
import com.apk.website.entity.TApkData;
import com.apk.website.entity.TApkDomainRelation;
import com.apk.website.entity.TPlateDomainRelation;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.mapper.ApkDomainRelationMapper;
import com.apk.website.mapper.PlateDomainRelationMapper;
import com.apk.website.vo.PlateDomainRelationVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.ReactiveTransaction;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/22
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
//@ActiveProfiles("test")
@ActiveProfiles("gcp_us_west1")
public class BindTest {

    @Autowired
    ApkDataMapper mapper;

    @Autowired
    ApkDomainRelationMapper relationMapper;

    @Test
    public void test() {
        for (int domainId = 34; domainId <= 35; domainId++) {

            // 本站点不重复
//            List<TApkDomainRelation> list = relationMapper.selectList(new LambdaQueryWrapper<>(TApkDomainRelation.class).eq(TApkDomainRelation::getDomainId, domainId));
//            List<Long> ids = list.stream().map(TApkDomainRelation::getApkId).collect(Collectors.toList());

            // 全站点不重复
            List<TApkDomainRelation> list = relationMapper.selectList(null);
            List<Long> ids = list.stream().map(TApkDomainRelation::getApkId).distinct().collect(Collectors.toList());

            LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class);
            wrapper.notIn(TApkData::getId, ids);
            // 日本
//            wrapper.eq(TApkData::getLanguage,"jp");
//            wrapper.eq(TApkData::getCategory, "アプリ");
//            wrapper.eq(TApkData::getCategory, "ゲーム");

            // 英文
            wrapper.eq(TApkData::getLanguage, "en");
            wrapper.eq(TApkData::getCategory, "Apps");
//            wrapper.eq(TApkData::getSource, "rank");
//            wrapper.in(TApkData::getSubCategory, "action","strategy","casino","sports");
            wrapper.eq(TApkData::getStatus, 0);
//
            List<TApkData> apkDataList = mapper.selectList(wrapper);

            // 按照subCategory分组，确保每个subCategory至少5条数据
            Map<String, List<TApkData>> groupedBySubCategory = apkDataList.stream()
                    .collect(Collectors.groupingBy(TApkData::getSubCategory));

            List<TApkData> finalList = new ArrayList<>();

            for (Map.Entry<String, List<TApkData>> entry : groupedBySubCategory.entrySet()) {
                List<TApkData> subCategoryList = entry.getValue();

                // 如果该subCategory的数量小于5条，取全部数据
                if (subCategoryList.size() < 5) {
                    finalList.addAll(subCategoryList);
                } else {
                    // 如果该subCategory的数量大于等于5条，随机选取5条数据
                    Collections.shuffle(subCategoryList); // 打乱顺序
                    finalList.addAll(subCategoryList.subList(0, 5)); // 取前5条
                }
            }

            // 如果最终选出的数据少于150条，随机从剩余数据中挑选补充
            int requiredCount = 150 - finalList.size();
            if (requiredCount > 0) {
                List<TApkData> remainingData = apkDataList.stream()
                        .filter(data -> !finalList.contains(data)) // 排除已经选中的数据
                        .collect(Collectors.toList());

                Collections.shuffle(remainingData); // 打乱顺序
                finalList.addAll(remainingData.subList(0, requiredCount)); // 补充到150条
            }



            List<TApkData> apkDataList1 = finalList.subList(0, 150);
            List<TApkDomainRelation> relations = new ArrayList<>();
            for (TApkData apkData : apkDataList1) {
                Long apkDataId = apkData.getId();
                TApkDomainRelation relation = new TApkDomainRelation();
                relation.setApkId(apkDataId);
                relation.setDomainId(domainId);
                relation.setStatus(0);
                relations.add(relation);
            }
            relationMapper.insertBatch(relations);
            log.info("domainId: {} 插入完成", domainId);


        }
    }


    @Test
    public void test_bind_cate() {
        for (int domainId = 37; domainId <= 37; domainId++) {

            LambdaQueryWrapper<TApkData> wrapper = new LambdaQueryWrapper<>(TApkData.class).eq(TApkData::getLanguage, "en").eq(TApkData::getSubCategory, "Finance").eq(TApkData::getStatus, 0);
            List<TApkData> apkDataList = mapper.selectList(wrapper);

            List<TApkDomainRelation> relations = new ArrayList<>();
            for (TApkData apkData : apkDataList) {
                Long apkDataId = apkData.getId();
                TApkDomainRelation relation = new TApkDomainRelation();
                relation.setApkId(apkDataId);
                relation.setDomainId(domainId);
                relation.setStatus(0);
                relations.add(relation);
            }
            relationMapper.insertBatch(relations);
            log.info("domainId: {} 插入完成", domainId);


        }
    }

    @Test
    public void test_bind_by_bundles() {
        List<Import.ExcelAppInfo> appInfos = fromExcel("/Users/<USER>/Desktop/new.xlsx");
        List<String> bundles = appInfos.stream().map(Import.ExcelAppInfo::getBundle).collect(Collectors.toList());
        List<TApkData> list = mapper.selectList(new LambdaQueryWrapper<>(TApkData.class).in(TApkData::getBundle, bundles));
//        Integer domainId = 5; // 29

        List<Integer> domainIds = Arrays.asList(3);
        for (Integer domainId : domainIds) {
            for (TApkData apkData : list) {
                Long apkDataId = apkData.getId();
                TApkDomainRelation relation = new TApkDomainRelation();
                relation.setApkId(apkDataId);
                relation.setDomainId(domainId);
                relation.setStatus(0);
                try {
                    relationMapper.insert(relation);
                } catch (Exception e) {
                    log.error("{} 已经存在", apkDataId);
                }
            }
        }

    }

    /**
     * 迁移配置
     */
    @Test
    public void test_2() {
        // 插入复制
        List<Integer> domains = Arrays.asList(47);
        Integer otherDomainId = 37;  // 被复制站点
        for (Integer domainId : domains) {

            // 删除原先配置
//            int delete = relationMapper.delete(new LambdaQueryWrapper<>(TApkDomainRelation.class).eq(TApkDomainRelation::getDomainId, domainId));
//            log.info("domainId: {} 删除完成: {} 条", domainId, delete);

            // 其他站点配置
            List<TApkDomainRelation> list = relationMapper.selectList(
                    new LambdaQueryWrapper<>(TApkDomainRelation.class).eq(TApkDomainRelation::getDomainId, otherDomainId
                    ));
            List<Long> ids = list.stream().map(TApkDomainRelation::getApkId).collect(Collectors.toList());

            List<TApkDomainRelation> relations = new ArrayList<>();
            for (Long apkId : ids) {
                TApkDomainRelation relation = new TApkDomainRelation();
                relation.setApkId(apkId);
                relation.setDomainId(domainId);
                relation.setStatus(0);
                relations.add(relation);
                try {
                    relationMapper.insert(relation);

                }catch (Exception e) {
                    log.warn("{} 已经存在", apkId);
                }
            }
//            relationMapper.insertBatch(relations);
            log.info("domainId: {} 插入完成", domainId);


        }
    }


    @Test
    public void test_rebind() {
        Integer domainId = 7;
        List<TApkData> apkDataList = mapper.select();
        Collections.shuffle(apkDataList);
        List<TApkData> apkDataList1 = apkDataList.subList(0, 300);
        List<TApkDomainRelation> list = new ArrayList<>();
        for (TApkData apkData : apkDataList1) {
            Long apkDataId = apkData.getId();
            TApkDomainRelation relation = new TApkDomainRelation();
            relation.setApkId(apkDataId);
            relation.setDomainId(domainId);
            relation.setStatus(0);
            list.add(relation);
        }
        relationMapper.insertBatch(list);

    }

    @Autowired
    private PlateDomainRelationMapper plateDomainRelationMapper;

    @Test
    public void bind_plate() {
//        LambdaQueryWrapper<TPlateDomainRelation> wrapper = new LambdaQueryWrapper<>(TPlateDomainRelation.class).eq(TPlateDomainRelation::getDomainId, 6).eq(TPlateDomainRelation::getDataType, 0);
//        List<TPlateDomainRelation> list = plateDomainRelationMapper.selectList(wrapper);
//        List<List<DataInfo>> collect = list.stream().map(TPlateDomainRelation::getData)
//                .map(s -> {
//                    return JSON.parseArray(s, DataInfo.class);
//                })
//                .collect(Collectors.toList());
//
//        List<Long> ids = new ArrayList<>();
//
//        for (List<DataInfo> dataInfos : collect) {
//            List<Long> bindIds = dataInfos.stream().map(DataInfo::getId).collect(Collectors.toList());
//            ids.addAll(bindIds);
//        }
//
//        for (Long id : ids) {
//            TApkDomainRelation relation = new TApkDomainRelation();
//            relation.setApkId(id);
//            relation.setDomainId(6);
//            relation.setStatus(0);
//            try {
//                relationMapper.insert(relation);
//            } catch (DuplicateKeyException e) {
//                log.warn("{} 已经存在", id);
//            }
//        }


        System.out.println();
//
    }

    @Data
    public static class DataInfo {
        private String banner;
//        private Long id;
    }

    private List<Import.ExcelAppInfo> fromExcel(String filePath) {
        List<Import.ExcelAppInfo> apps = new ArrayList<>();
        EasyExcel.read(filePath, Import.ExcelAppInfo.class, new PageReadListener<Import.ExcelAppInfo>(dataList -> {
            for (Import.ExcelAppInfo excelAppInfo : dataList) {
                apps.add(excelAppInfo);
            }
        })).sheet().doRead();
        return apps;
    }

    public List<String> bundles() {
        String filePath = "/Users/<USER>/Desktop/new.xlsx"; // 修改为你的文件路径
        List<String> packageNames = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                packageNames.add(line.trim());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return packageNames;
    }
}
