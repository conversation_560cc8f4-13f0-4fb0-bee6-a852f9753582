import com.alibaba.fastjson.JSON;
import com.apk.website.Application;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.entity.TStickerData;
import com.apk.website.mapper.TStickerInfoDataMapper;
import com.apk.website.schedule.utils.QiniuUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.apk.website.vo.TStickerPreviewData;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.codehaus.jackson.map.ObjectMapper;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Description;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@ActiveProfiles("gcp_us_west1")
@Slf4j
public class LineStore {
    @Autowired
    QiniuUtils qiniuUtils;

    @Autowired
    private TStickerInfoDataMapper tStickerInfoDataMapper;


    // 列表爬取url
    private static final String BASE_URL = "https://store.line.me/stickershop/showcase/top/zh-Hant?page=";
    // 列表的最大页数
    int MAX_PAGE = 103;


    public static void main(String[] args) throws Exception {

    }

    @Data
    static class Category {
        private String title;
        private int count;
        private String path;
        private int id;
    }

    @Test
    public void getList() {
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        for (int i = 0; i < MAX_PAGE; i++) {
            String url = BASE_URL + i;
            Request request = OkHttpClientUtils.buildRequest(url);
            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String body = response.body().string();
                    Document parse = Jsoup.parse(body);
                    Elements elements = null;
                    try {
                        elements = parse.select("div.MdCMN02List").first().select("ul").first().select("li");
                    }catch (Exception e) {
                        log.info("爬取第{}页错误", i);
                        continue;
                    }
                    log.info("第{}页有{}个元素", i, elements.size());
                    log.info("开始爬取第{}页", i);
                    int j = 0;
                    for (Element element : elements) {

                        String href = element.select("a").attr("href");
                        log.info("href:{}", href);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }


    @Test
    // 获取分类链接
    public void getCategoryPath() {
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://store.line.me/stickershop/showcase/top/zh-Hant";
        Request request = OkHttpClientUtils.buildRequest(url);
        try (Response response = clientInstance.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String body = response.body().string();
                Document parse = Jsoup.parse(body);
                Elements elements = parse.select("ul.mdCMN13Ul").first().select("li");
                elements.remove(0); // 移除全部这一行
                for (Element element : elements) {
                    Category category = new Category();
                    String href = element.select("a").attr("href");
                    String categoryName = element.select("a").text();
                    String count = element.select("em.mdCMN13Count").text().replace("(", "").replace(")", "").replace(",", "");
                    log.info("已爬取{}，链接：{}，数量：{}", categoryName, href, count);
                    category.setPath(href);
                    category.setCount(Integer.parseInt(count));
                    category.setTitle(categoryName);
                    category.setId(Integer.parseInt(href.replace("?category=", "")));

                    log.info(String.valueOf(category));
                    getStickerListByCategory(category);
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    // 根据分类获取链接
    public void getStickerListByCategory(Category category) {
        log.info("开始爬取{}，链接：{}，数量：{}", category.getTitle(), category.getPath(), category.getCount());
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://store.line.me/stickershop/showcase/top/zh-Hant";

        int MAX_PAGE = (int) Math.ceil((double) category.getCount() / 36);
        for (int pageNum = 1; pageNum < MAX_PAGE; pageNum++) {
            Request request = OkHttpClientUtils.buildRequest(url + category.getPath() + "&page=" + pageNum);
            try (Response response = clientInstance.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    String body = response.body().string();
                    if (body.contains("您要求存取的網頁不存在。")) {
                        log.info("该分类已爬完");
                        break;
                    }
                    Document parse = Jsoup.parse(body);
                    Elements elements = parse.select("div.MdCMN02List").first().select("ul").first().select("li");
                    for (Element element : elements) {
                        String href = element.select("a").attr("href");
                        String title = element.select("a").select("p").text();
                        String stickerId = href.replaceAll(".*/stickershop/product/([0-9]+)/.*", "$1");
                        if (tStickerInfoDataMapper.selectById(stickerId) != null){
                            log.info("{}已存在",stickerId);
                            continue;
                        }
                        getStickerDetail(href, title, category);
                    }
                    pageNum++;
                }else {
                    log.info("爬取失败");
                    break;
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

    }

    // 获取单个贴图详情
    public void getStickerDetail(String href, String title, Category category) {
        log.info("开始爬取{}，链接：{}", title, href);
        OkHttpClient clientInstance = OkHttpClientUtils.CLIENT.getClientInstance();
        String baseUrl = "https://store.line.me";
        String url = baseUrl + href;
        Request request = OkHttpClientUtils.buildRequest(url);
        try (Response response = clientInstance.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String body = response.body().string();
                Document parse = Jsoup.parse(body);
                Element mainElement = parse.select("div.mdBox03Inner01").first();
                TStickerData stickerData = new TStickerData();

                String stickerId = href.replaceAll(".*/stickershop/product/([0-9]+)/.*", "$1");
                String stickerTitle = mainElement.select("p.mdCMN38Item01Ttl").text();
                String stickerCover = mainElement.select("div.mdCMN38Img > img").attr("src");
                log.info("正在将封面图上传到七牛云，stickerCover:{}", stickerCover);
                stickerCover = qiniuUtils.downloadAndUpload(stickerCover, "sticker/" + stickerId + "/cover", qiniuUtils.getQiniuToken(), 3);
                log.info("封面图上传成功，stickerCover:{}", stickerCover);
                String AuthorName = mainElement.select("a.mdCMN38Item01Author").text();
                String authorId = mainElement.select("a.mdCMN38Item01Author").select("a").attr("href").replaceAll(".*/author/(\\d+)/.*", "$1");
                String price = mainElement.select("p.mdCMN38Item01Price").text().replace("US$", "");
                String description = mainElement.select("p.mdCMN38Item01Txt").text();

                @Data class StickerData {
                    private String fallbackStaticUrl;
                    private String animationUrl;
                    private String popupUrl;
                    private String soundUrl;

                    public StickerData(String fallbackStaticUrl, String animationUrl, String popupUrl, String soundUrl) {
                        this.fallbackStaticUrl = fallbackStaticUrl;
                        this.animationUrl = animationUrl;
                        this.popupUrl = popupUrl;
                        this.soundUrl = soundUrl;
                    }
                }
                List<StickerData> stickersList = new ArrayList<>();

                Elements stickerElements = mainElement.select("ul.FnStickerList").first().select("li");
                for (Element stickerElement : stickerElements) {
                    String data = stickerElement.attr("data-preview");
                    ObjectMapper mapper = new ObjectMapper();
                    TStickerPreviewData stickerPreviewJson = mapper.readValue(data, TStickerPreviewData.class);
                    String animationUrl = stickerPreviewJson.getAnimationUrl();
                    String fallbackStaticUrl = stickerPreviewJson.getFallbackStaticUrl();
                    String popupUrl = stickerPreviewJson.getPopupUrl();
                    String soundUrl = stickerPreviewJson.getSoundUrl();
                    String token = qiniuUtils.getQiniuToken();

                    if (!animationUrl.isEmpty()) {
                        String AnimationQiNiuKey = "sticker/" + stickerId + "/" + animationUrl.replaceAll(".*/sticker/(\\d+)/.*", "$1") + "_animation";
                        log.info("正在将动图上传到七牛云，animationUrl:{}", animationUrl);
                        animationUrl = qiniuUtils.downloadAndUpload(animationUrl,AnimationQiNiuKey, token, 3);
                        log.info("动图上传成功，animationUrl:{}", animationUrl);
                    }

                    if (!popupUrl.isEmpty()) {
                        String popupQiNiuKey = "sticker/" + stickerId + "/" + popupUrl.replaceAll(".*/sticker/(\\d+)/.*", "$1") + "_popup";
                        log.info("正在将popup上传到七牛云，popupUrl:{}", popupUrl);
                        popupUrl = qiniuUtils.downloadAndUpload(popupUrl, popupQiNiuKey, token, 3);
                        log.info("popup上传成功，popupUrl:{}", popupUrl);
                    }

                    if (!soundUrl.isEmpty()) {
                        String soundQiNiuKey = "sticker/" + stickerId + "/" + soundUrl.replaceAll(".*/sticker/(\\d+)/.*", "$1") + "_sound";
                        log.info("正在将sound上传到七牛云，soundUrl:{}", soundUrl);
                        soundUrl = qiniuUtils.downloadAndUpload(soundUrl, soundQiNiuKey, token, 3);
                        log.info("sound上传成功，soundUrl:{}", soundUrl);
                    }

                    if (!fallbackStaticUrl.isEmpty()) {
                        String fallbackStaticQiNiuKey = "sticker/" + stickerId + "/" + fallbackStaticUrl.replaceAll(".*/sticker/(\\d+)/.*", "$1") + "_fallbackStatic";
                        log.info("正在将静态图上传到七牛云，fallbackStaticUrl:{}", fallbackStaticUrl);
                        fallbackStaticUrl = qiniuUtils.downloadAndUpload(fallbackStaticUrl, fallbackStaticQiNiuKey, token, 3);
                        log.info("静态图上传成功，fallbackStaticUrl:{}", fallbackStaticUrl);
                    }

                    stickersList.add(new StickerData(fallbackStaticUrl, animationUrl, popupUrl, soundUrl));
                }

                stickerData.setId(Integer.parseInt(stickerId));
                stickerData.setTitle(stickerTitle);
                stickerData.setCover(stickerCover);
                stickerData.setAuthorName(AuthorName);
                stickerData.setAuthorId(Integer.parseInt(authorId));
                stickerData.setPrice(new BigDecimal(price));
                stickerData.setDescription(description);
                stickerData.setCategoryId(category.getId());
                stickerData.setCategoryName(category.getTitle());
                stickerData.setStickersList(JSON.toJSONString(stickersList));
                stickerData.setType("0");
                stickerData.setCreateTime(new Date());
                stickerData.setUpdateTime(new Date());
                log.info("即将插入: {}", stickerData);
                try {
                    tStickerInfoDataMapper.insert(stickerData);
                    log.info("插入{}成功", stickerData.getTitle());
                } catch (DuplicateKeyException e) {
                    log.warn("已存在");
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
