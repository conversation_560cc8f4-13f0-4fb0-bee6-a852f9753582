import com.apk.website.Application;
import com.apk.website.dto.admin.ApkDataReq;
import com.apk.website.entity.TApkData;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.schedule.utils.GoogleUtils;
import com.apk.website.schedule.utils.QiniuUtils;
import com.apk.website.vo.ExtraInfo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/28
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@ActiveProfiles("gcp_us_west1")
public class Import2 {

    @Autowired
    private GoogleUtils googleUtils;

    @Autowired
    private ApkDataMapper mapper;

    @Autowired
    private QiniuUtils qiniuUtils;


    // 谷歌爬取入库
    // todo 这里图片上传在七牛云，需要手动迁移到cloudflare，文档有教程

    @Test
    public void test_import() {


//        List<String> appInfos = bundles(); // 读取txt文件
        List<String> appInfos = Arrays.asList("com."); // 手动添加包名
//        String googleBaseUrl = "https://play.google.com/store/apps/details?id=%s&hl=us&gl=en";
        String googleBaseUrl = "https://play.google.com/store/apps/details?id=%s";
        String token = qiniuUtils.getQiniuToken();

        for (String bundle : appInfos) {

            TApkData tApkData = mapper.selectOne(new LambdaQueryWrapper<>(TApkData.class).eq(TApkData::getBundle, bundle).eq(TApkData::getLanguage, "en"));
            if (tApkData != null) {
                log.error("{} 已经存在", bundle);
                continue;
            }

            try {
                String category = "";
                String subCategory = googleUtils.buildSubCategoryFromPac(bundle);

                String url = String.format(googleBaseUrl, bundle);
                Document doc = googleUtils.getGoogleHtml(url);
                if (doc == null) {
                    log.error("{} 下架", bundle);
                    continue;
                }
                TApkData apkData = new TApkData();

                String appName = googleUtils.buildAppName(doc, "");
                String developer = googleUtils.buildDeveloper(doc);

                String img = googleUtils.buildIconByQiniu(doc, token);
                String banners = googleUtils.buildBannersByQiniu(doc, token);
                String version = googleUtils.buildLatestVersion(url);
                String video = googleUtils.buildVideo(doc);
                String updateDate = googleUtils.buildUpdateDate(doc);
                String email = googleUtils.buildEmail(doc);
                float score = googleUtils.buildScore(doc);
                String installs = googleUtils.buildInstalls(doc);
                String description = googleUtils.buildDescription(doc);

                apkData.setCategory(category);
                apkData.setSubCategory(subCategory);
                apkData.setAppName(appName);
                apkData.setImg(img);
                apkData.setBanners(banners);

                apkData.setBundle(bundle);
                apkData.setUpdateDate(updateDate);
                apkData.setScore(score);
                apkData.setInstalls(installs);
                apkData.setDetail("");
                apkData.setLanguage("en");
                apkData.setStatus(0);
                apkData.setPicture("");
                apkData.setOfferUrl("");
                apkData.setApkUrl("");
                apkData.setGoogleUrl(url);
                apkData.setAppleUrl("");
                apkData.setDescription(description);
                apkData.setVideo(video);

                ApkDataReq req = new ApkDataReq();
                req.setSubCategory(subCategory);
                req.setEmail(email);
                req.setLatestVersion(version);
                req.setOfferedBy(developer);

                String extraInfo = ExtraInfo.buildExtraInfo(req);

                apkData.setExtraInfo(extraInfo);

                try {
                    mapper.insert(apkData);
                    log.info("{} 添加成功", bundle);
                }catch (Exception e) {
                    log.error("{} 已经存在", bundle);
                }
            }catch (Exception e) {
//                log.error("{} 出现异常： {}", appInfo.getBundle(), e);
            }


        }
    }

    // 读取txt文件包名
    public List<String> bundles() {
        String filePath = "/Users/<USER>/dev/1.txt"; // 修改为你的文件路径
        List<String> packageNames = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                packageNames.add(line.trim());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return packageNames;
    }

}
