import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.apk.website.Application;
import com.apk.website.common.utils.OkHttpClientUtils;
import com.apk.website.entity.GGamesHub;
import com.apk.website.entity.GamesHubResponse;
import com.apk.website.mapper.GGamesHubMapper;
import com.apk.website.schedule.utils.QiniuUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@ActiveProfiles("gcp_us_west1")
@Slf4j
public class Gameshub1 {

    @Autowired
    QiniuUtils qiniuUtils;

    @Autowired
    GGamesHubMapper mapper;

    @Test
    public void testConnection() throws IOException {
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://gameshub1.com/";
        
        Request request = OkHttpClientUtils.buildRequest(url);
        Response resp = httpClient.newCall(request).execute();
        
        if (resp.isSuccessful()) {
            Document document = Jsoup.parse(resp.body().string());
            log.info("Successfully connected to gameshub1.com");
        } else {
            log.error("Failed to connect to gameshub1.com, response code: {}", resp.code());
        }
    }
    
    @Test
    public void testGetGames() throws IOException {
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();
        String url = "https://gameshub1.com/";
        
        Request request = OkHttpClientUtils.buildRequest(url);
        Response resp = httpClient.newCall(request).execute();
        
        if (resp.isSuccessful()) {
            Document document = Jsoup.parse(resp.body().string());
            Elements gameElements = document.select(".video-item");
            log.info("Found {} games", gameElements.size());
            List<GameInfo> games = new ArrayList<>();
            for (Element gameElement : gameElements) {
                String title = gameElement.select(".video-name").text();
                log.info("Game: {}", title);
                // 修复：正确获取游戏链接，原代码选择器错误
                String gameUrl = gameElement.attr("href");
                String imageUrl = gameElement.select(".cover").attr("src");
                games.add(new GameInfo(title, gameUrl, imageUrl));
            }
            log.info("Found {} games", games.size());
            log.info("Games: {}", JSON.toJSONString(games));
        } else {
            log.error("Failed to connect to gameshub1.com, response code: {}", resp.code());
        }
    }

    public String getVideoIframeString(String path) throws IOException {
        OkHttpClient httpClient = OkHttpClientUtils.CLIENT.getClientInstance();

//        ja  zh_TW
//        String jsUrl = "https://www.gameshub1.com/_nuxt/static/1737355633/video/{{path}}/payload.js";

//        en
//        String jsUrl = "https://108.anime.gameshub1.com/_nuxt/static/1739175798/video/{{path}}/payload.js";

//        jp2
        String jsUrl = "https://2.minecraft.gameshub1.com/_nuxt/static/1745807092/video/{{path}}/payload.js";


        jsUrl = jsUrl.replace("{{path}}", path);
        Request request = OkHttpClientUtils.buildRequest(jsUrl);
        Response resp = httpClient.newCall(request).execute();
        if (resp.isSuccessful()) {
            String res = resp.body().string();
            // 视频播放地址
            Pattern videoUrlPattern = Pattern.compile("video_url:\"(.*?)\"");
            Matcher matcher = videoUrlPattern.matcher(res);
            String videoUrl = "";
            if (matcher.find()) {
                videoUrl = matcher.group(1);
            }
            return videoUrl;
        } else {
            log.error("Failed to connect to gameshub1.com, response code: {}", resp.code());
            return null;
        }
    }

    public String utf8Decode (String str) {
        return  str.replace("\\u002F", "/");
    }

    @Test
    public void start() throws Exception {
        String jsonStr = new String(Files.readAllBytes(Paths.get("/Users/<USER>/code/java/djs-apk-website/src/test/json/gameshub-ja2.json")));
        GamesHubResponse resp = JSONObject.parseObject(jsonStr, GamesHubResponse.class);
        List<GamesHubResponse.GameItem> list = resp.getData().getList();
        int i = 1;
        for (GamesHubResponse.GameItem gameItem : list) {
            log.info("开始处理 第 {} 个",  i);
            String token = qiniuUtils.getQiniuToken();
            String imgUrl = "https://bunchthings.com/cdn-cgi/image/w=1280,h=720,f=auto,fit=cover/" + gameItem.getCover();
            String qiniuKey = gameItem.getCover().split("/")[2].replace(".webp", "").replace(".jpg", "");
            String qiniuCoverPath = qiniuUtils.downloadAndUpload(imgUrl,qiniuKey, token, 3);

            String videoUrl = utf8Decode(getVideoIframeString(gameItem.getPath()));

            GGamesHub hub = new GGamesHub();

            hub.setId(gameItem.getId());
            hub.setName(utf8Decode(gameItem.getName()));
            hub.setPath(utf8Decode(gameItem.getPath()));
            hub.setVideoUrl(videoUrl);

            hub.setCover(qiniuCoverPath);

            hub.setPath(gameItem.getPath());
            hub.setSeriesId(gameItem.getSeries_id());
            hub.setSeriesName(gameItem.getSeries_name());
            hub.setDuration(gameItem.getDuration());
            hub.setView(gameItem.getView());
            hub.setLang("jp");
            hub.setCreateTime(new Date());
            hub.setUpdateTime(new Date());

            try {
                int insert = mapper.insert(hub);
                if (insert != 0) {
                    log.info("插入成功 {}/{}", i ,list.size());
                } else {
                    log.info("插入失败{}", gameItem.getName());
                }
            }catch (DuplicateKeyException e) {
                log.warn("{}, 已经存在", gameItem.getId());
            }

            i++;

        }
//       String url = getVideoIframeString("-sv--8132");
//       log.info("Video Info: {}", url);
    }

    @Data
    static class GameInfo {
        private String title;
        private String url;
        private String imageUrl;

        // 修复：正确实现构造函数，为成员变量赋值
        public GameInfo(String title, String url, String imageUrl) {
            this.title = title;
            this.url = url;
            this.imageUrl = imageUrl;
        }
    }




}