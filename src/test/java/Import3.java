import com.alibaba.fastjson.JSON;
import com.apk.website.Application;
import com.apk.website.dto.AppExcelResp;
import com.apk.website.entity.TApkData;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.mapper.ApkDomainRelationMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 废弃
 * @create 2025/5/28
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@ActiveProfiles("gcp_us_west1")
public class Import3 {

    @Resource
    private ApkDataMapper mapper;

    @Autowired
    ApkDomainRelationMapper relationMapper;

    @Test
    public void test() throws IOException {
        // 1. 读取 JSON 文件内容
        String jsonStr = new String(Files.readAllBytes(new File("/Users/<USER>/projects/python-script/other/output.json").toPath()), StandardCharsets.UTF_8);

        // 2. 转换为 Java 对象
        AppExcelResp response = JSON.parseObject(jsonStr, AppExcelResp.class);

        List<String> bundles = response.getResult().stream()
                .flatMap(pageResult -> pageResult.getItems().stream())
                .flatMap(item -> item.getApps().stream())
                .map(appData -> appData.getBundle())
                .collect(Collectors.toList());

        List<TApkData> datas = mapper.selectList(new LambdaQueryWrapper<>(TApkData.class).eq(TApkData::getLanguage, "en").in(TApkData::getBundle, bundles));


        Map<String, TApkData> bundleToDataMap = datas.stream()
                .collect(Collectors.toMap(TApkData::getBundle, t -> t, (a, b) -> a));

        response.getResult().forEach(pageResult -> {
            pageResult.getItems().forEach(item -> {
                item.getApps().forEach(appData -> {
                    TApkData matched = bundleToDataMap.get(appData.getBundle());
                    if (matched != null && matched.getStatus() == 0) {
                        appData.setImg(matched.getImg());
                        appData.setScore(matched.getScore());
                        appData.setInstalls(matched.getInstalls());
                        appData.setBanners(matched.getBanners());
                        Elements elements = Jsoup.parse(matched.getDetail()).body().select("p");
                        for (Element element : elements) {
                            String desc = element.text();
                            if (StringUtils.isNotBlank(desc)) {
                                appData.setDesc(desc);
                                break;
                            }
                        }
                        appData.setCategory(matched.getCategory());
                        appData.setSubCategory(matched.getSubCategory());
                    }
                });
            });
        });

        String jsonString = JSON.toJSONString(response);
        Files.write(Paths.get("/Users/<USER>/Desktop/output_updated.json"), jsonString.getBytes(StandardCharsets.UTF_8));

    }

}
