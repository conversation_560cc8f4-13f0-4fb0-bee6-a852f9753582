import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import com.apk.website.Application;
import com.apk.website.constant.StatusConstant;
import com.apk.website.dto.AppExcelResp;
import com.apk.website.entity.TApkData;
import com.apk.website.mapper.ApkDataMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@ActiveProfiles("gcp_us_west1")
public class Import4 {

    @Autowired
    private ApkDataMapper mapper;

    // 转为json
    @Test
    public void test() {
        List<ExcelAppInfo> excelAppInfos = filterList("Financial", "en", "/Users/<USER>/Downloads/work/3.xlsx");
        Map<String, ExcelAppInfo> resultMap = excelAppInfos.stream().collect(Collectors.toMap(ExcelAppInfo::getBundle, t -> t));


        List<String> bundles = excelAppInfos.stream().map(ExcelAppInfo::getBundle).collect(Collectors.toList());
        List<TApkData> list = mapper.selectList(new LambdaQueryWrapper<>(TApkData.class).in(TApkData::getBundle, bundles).eq(TApkData::getStatus, 0).eq(TApkData::getLanguage, "en"));
        Map<String, TApkData> map = list.stream()
                .collect(Collectors.toMap(TApkData::getBundle, t -> t));

        List<AppData> apps = new ArrayList<>();

        map.forEach((k, v) -> {
            ExcelAppInfo excelAppInfo = resultMap.get(k);
            AppData appData = new AppData();
            appData.setBundle(excelAppInfo.getBundle());
            appData.setAppName(excelAppInfo.getAppName());
            appData.setBanners(v.getBanners());
            appData.setGoogleUrl(v.getGoogleUrl());
            appData.setAppleUrl(v.getAppleUrl());
            appData.setImg(v.getImg());
            appData.setScore(v.getScore());
            appData.setInstalls(v.getInstalls());
            appData.setCategory(v.getCategory());
            appData.setSubCategory(v.getSubCategory());
            appData.setThirdCategory(excelAppInfo.getCategories());
            appData.setType("");
            Elements elements = Jsoup.parse(v.getDetail()).body().select("p");
            for (Element element : elements) {
                String desc = element.text();
                if (StringUtils.isNotBlank(desc)) {
                    appData.setDesc(desc);
                    break;
                }
            }
            apps.add(appData);
        });

        String jsonString = JSON.toJSONString(apps);
        System.out.println();

    }

    // 过滤库里没有的app
    public List<Import4.ExcelAppInfo> filterList(String category, String language, String filePath) {
        List<TApkData> apkDataList = mapper.selectList(new LambdaQueryWrapper<>(TApkData.class).eq(TApkData::getCategory, category).eq(TApkData::getLanguage, language).eq(TApkData::getStatus, 0));

        List<Import4.ExcelAppInfo> appInfos = fromExcel(filePath);

        // 提取 apkDataList 中的所有 bundle
        List<String> apkDataBundles = apkDataList.stream()
                .map(TApkData::getBundle)
                .collect(Collectors.toList());

        // 筛选 appInfos 中那些 bundle 不在 apkDataList 中的项
        List<Import4.ExcelAppInfo> filteredAppInfos = appInfos.stream()
                .filter(appInfo -> !apkDataBundles.contains(appInfo.getBundle()))
                .collect(Collectors.toList());
        return filteredAppInfos;
    }

    // 读取excel
    private List<Import4.ExcelAppInfo> fromExcel(String filePath) {
        List<Import4.ExcelAppInfo> apps = new ArrayList<>();
        EasyExcel.read(filePath, Import4.ExcelAppInfo.class, new PageReadListener<Import4.ExcelAppInfo>(dataList -> {
            for (Import4.ExcelAppInfo excelAppInfo : dataList) {
                apps.add(excelAppInfo);
            }
        })).sheet().doRead();
        return apps;
    }


    // excel字段
    @Data
    public static class ExcelAppInfo {
        private String appName;
        private String bundle;
        private String googleUrl;
        private String Categories;
    }

    // 前端需要的字段
    @Data
    public static class AppData {
        private String bundle;
        private String appName;
        private String banners;
        private String googleUrl;
        private String appleUrl;
        private String img;
        private float score;
        private String installs;
        private String desc;
        private String category;
        private String subCategory;
        private String ThirdCategory;
        private String type;
    }


}
