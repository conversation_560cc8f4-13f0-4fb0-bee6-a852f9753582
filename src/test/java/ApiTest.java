import com.alibaba.fastjson.JSON;
import com.apk.website.Application;
import com.apk.website.ai.Rewrite;
import com.apk.website.entity.TApkData;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.schedule.AppnstoreSchedule;
import com.apk.website.schedule.GoogleMonitorSchedule;
import com.apk.website.schedule.RelationSchedule;
import com.apk.website.schedule.utils.GoogleUtils;
import com.apk.website.vo.ExtraInfo;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.rmi.server.UnicastRemoteObject;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description
 * @create 2025/5/19
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
//@ActiveProfiles("test")
@ActiveProfiles("gcp_us_west1")
public class ApiTest {

    @Autowired
    private RelationSchedule relationSchedule;

    @Autowired
    private GoogleUtils googleUtils;

    @Test
    public void test_relation_schedule() {
        relationSchedule.relation();
    }

    @Autowired
    private ApkDataMapper apkDataMapper;

    @Test
    public void test_update_date() {
        List<TApkData> list =  apkDataMapper.select();
        int count = 0;
        for (TApkData apkData : list) {
            try {
                Document googleHtml = googleUtils.getGoogleHtml(apkData.getGoogleUrl());
                if (googleHtml == null) {
                    log.warn("apkId: {} 谷歌下架, 第 {} 个", apkData.getId(), ++count);
                    continue;
                }
                String updateDate = googleUtils.buildUpdateDate(googleHtml);
                apkData.setUpdateDate(updateDate);
                apkDataMapper.updateById(apkData);
                log.info("apkId: {} 谷歌更新日期: {}", apkData.getId(), updateDate);
            }catch (Exception e) {
                log.error("exception: {}", e);
            }

        }
    }

    @Test
    public void test_update_version() {
        List<TApkData> list =  apkDataMapper.select();
        for (TApkData apkData : list) {

            String version = googleUtils.buildLatestVersion(apkData.getGoogleUrl());
            String extraInfo = apkData.getExtraInfo();
            ExtraInfo extraInfo1 = JSON.parseObject(extraInfo, ExtraInfo.class);
            extraInfo1.setLatestVersion(version);
            String json = JSON.toJSONString(extraInfo1);
            apkData.setExtraInfo(json);

        }
    }

    @Autowired
    GoogleMonitorSchedule monitorSchedule;
    @Test
    public void test_monitor() {
        monitorSchedule.monitor();
    }

    @Autowired
    public AppnstoreSchedule schedule;

    @Test
    public void test_appnstore() {
        schedule.pull();
    }

    @Autowired
    Rewrite rewrite;

    @Test
    public void test() {
        rewrite.updateByAi();
    }

}
