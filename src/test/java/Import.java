import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.fastjson.JSON;
import com.apk.website.Application;
import com.apk.website.dto.admin.ApkDataReq;
import com.apk.website.entity.TApkData;
import com.apk.website.mapper.ApkDataMapper;
import com.apk.website.schedule.utils.GoogleUtils;
import com.apk.website.schedule.utils.QiniuUtils;
import com.apk.website.vo.ExtraInfo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.nodes.Document;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.FileReader;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description excel读取入库
 * @create 2025/5/28
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@ActiveProfiles("gcp_us_west1")
public class Import {

    @Autowired
    private GoogleUtils googleUtils;

    @Autowired
    private ApkDataMapper mapper;

    @Autowired
    private QiniuUtils qiniuUtils;

    @Test
    public void test_2() {
        List<TApkData> list = mapper.select();
        for (TApkData apkData : list) {
            apkData.setDetail(apkData.getDescription());
            mapper.updateById(apkData);
        }
    }

    // 谷歌爬取入库
    // todo 这里图片上传在七牛云，需要手动迁移到cloudflare，文档有教程
    @Test
    public void test_import() {

        String category = "Apps";
        String language = "en";
        String filePath = "/Users/<USER>/Desktop/apps.xlsx";

//        String googleBaseUrl = "https://play.google.com/store/apps/details?id=%s&hl=ja&gl=jp";
        String googleBaseUrl = "https://play.google.com/store/apps/details?id=%s&hl=en&gl=us";
        List<ExcelAppInfo> appInfos = filterList(category, language, filePath);
        String token = qiniuUtils.getQiniuToken();

        Integer count = 0;
        for (ExcelAppInfo appInfo : appInfos) {
            try {
                String bundle = appInfo.getBundle();
                String developer = appInfo.getDeveloper();
                String appName = appInfo.getAppName();
                String subCategory = appInfo.getSubCategory();
                String url = String.format(googleBaseUrl, bundle);
                Document doc = googleUtils.getGoogleHtml(url);
                if (doc == null) {
                    log.error("{} 下架", bundle);
                    continue;
                }
                TApkData apkData = new TApkData();

                String img = googleUtils.buildIconByQiniu(doc, token);
                String banners = googleUtils.buildBannersByQiniu(doc, token);
                String version = googleUtils.buildLatestVersion(url);
                String video = googleUtils.buildVideo(doc);
                String updateDate = googleUtils.buildUpdateDate(doc);
                String email = googleUtils.buildEmail(doc);
                float score = googleUtils.buildScore(doc);
                String installs = googleUtils.buildInstalls(doc);
                String description = googleUtils.buildDescription(doc);

                apkData.setCategory(category);
                apkData.setSubCategory(subCategory);
                apkData.setAppName(appName);
                apkData.setImg(img);
                apkData.setBanners(banners);

                apkData.setBundle(bundle);
                apkData.setUpdateDate(updateDate);
                apkData.setScore(score);
                apkData.setInstalls(installs);
                apkData.setDetail("");
                apkData.setLanguage(language);
                apkData.setStatus(1);
                apkData.setPicture("");
                apkData.setOfferUrl("");
                apkData.setApkUrl("");
                apkData.setGoogleUrl(url);
                apkData.setAppleUrl("");
                apkData.setDescription(description);
                apkData.setVideo(video);

                ApkDataReq req = new ApkDataReq();
                req.setSubCategory(subCategory);
                req.setEmail(email);
                req.setLatestVersion(version);
                req.setOfferedBy(developer);

                String extraInfo = ExtraInfo.buildExtraInfo(req);

                apkData.setExtraInfo(extraInfo);

                mapper.insert(apkData);
                count++;
                log.info("{} 添加成功, 进度: {}", bundle, count + "/" + appInfos.size());
            }catch (Exception e) {
                log.error("{} 出现异常： {}", appInfo.getBundle(), e);
            }


        }
    }

    // 过滤出库里没有的
    public List<ExcelAppInfo> filterList(String category, String language, String filePath) {
        List<TApkData> apkDataList = mapper.selectList(new LambdaQueryWrapper<>(TApkData.class).eq(TApkData::getCategory, category).eq(TApkData::getLanguage, language));

        List<ExcelAppInfo> appInfos = fromExcel(filePath);

        // 提取 apkDataList 中的所有 bundle
        List<String> apkDataBundles = apkDataList.stream()
                .map(TApkData::getBundle)
                .collect(Collectors.toList());

        // 筛选 appInfos 中那些 bundle 不在 apkDataList 中的项
        List<ExcelAppInfo> filteredAppInfos = appInfos.stream()
                .filter(appInfo -> !apkDataBundles.contains(appInfo.getBundle()))
                .collect(Collectors.toList());
        return filteredAppInfos;
    }

    // 读取excel
    private List<ExcelAppInfo> fromExcel(String filePath) {
        List<ExcelAppInfo> apps = new ArrayList<>();
        EasyExcel.read(filePath, ExcelAppInfo.class, new PageReadListener<ExcelAppInfo>(dataList -> {
            for (ExcelAppInfo excelAppInfo : dataList) {
                apps.add(excelAppInfo);
            }
        })).sheet().doRead();
        return apps;
    }


    // excel字段
    @Data
    public static class ExcelAppInfo {
        private String bundle;
        private String appName;
        private String subCategory;
        private String developer;
    }

}
